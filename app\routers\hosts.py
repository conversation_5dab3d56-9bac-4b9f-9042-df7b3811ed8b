"""
主机管理API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.models import HostCreate, HostUpdate, HostResponse, User
from app.crud import HostCRUD, AuditLogCRUD
from app.ssh_manager import get_ssh_manager, SSHManager
from app.auth import get_current_active_user, require_host_write, require_host_delete

router = APIRouter(prefix="/api/hosts", tags=["hosts"])


@router.post("/", response_model=HostResponse, status_code=status.HTTP_201_CREATED)
async def create_host(
    host_data: HostCreate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """创建新主机"""
    try:
        host = await HostCRUD.create(db, host_data, current_user.id)

        # 记录操作
        await AuditLogCRUD.create(
            db, current_user.id, "host_created", "host", str(host.id),
            {"name": host.name, "ip": host.ip}, request.client.host
        )

        return host
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建主机失败: {str(e)}"
        )


@router.get("/", response_model=List[HostResponse])
async def get_hosts(
    skip: int = 0,
    limit: int = 100,
    group_id: Optional[int] = Query(None, description="按分组筛选"),
    tags: Optional[str] = Query(None, description="按标签筛选，多个标签用逗号分隔"),
    search: Optional[str] = Query(None, description="搜索主机名或IP"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取主机列表"""
    # 解析标签
    tag_list = None
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

    hosts = await HostCRUD.get_all(
        db, skip=skip, limit=limit,
        group_id=group_id, tags=tag_list, search=search
    )
    return hosts


@router.get("/{host_id}", response_model=HostResponse)
async def get_host(
    host_id: int,
    db: AsyncSession = Depends(get_db_session)
):
    """获取单个主机信息"""
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    return host


@router.put("/{host_id}", response_model=HostResponse)
async def update_host(
    host_id: int,
    host_data: HostUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """更新主机信息"""
    host = await HostCRUD.update(db, host_id, host_data)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    return host


@router.delete("/{host_id}")
async def delete_host(
    host_id: int,
    db: AsyncSession = Depends(get_db_session)
):
    """删除主机"""
    success = await HostCRUD.delete(db, host_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    return {"message": "主机删除成功"}


@router.post("/{host_id}/test")
async def test_host_connection(
    host_id: int,
    db: AsyncSession = Depends(get_db_session),
    ssh_manager: SSHManager = Depends(get_ssh_manager)
):
    """测试主机连接"""
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    
    result = await ssh_manager.test_connection(host)
    
    if result.success:
        # 更新最后连接时间
        await HostCRUD.update_last_connected(db, host_id)
        return {
            "success": True,
            "message": "连接测试成功",
            "output": result.output
        }
    else:
        return {
            "success": False,
            "message": "连接测试失败",
            "error": result.error
        }


@router.get("/tags", response_model=List[str])
async def get_all_tags(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有主机标签"""
    tags = await HostCRUD.get_all_tags(db)
    return tags


@router.post("/{host_id}/tags")
async def add_host_tags(
    host_id: int,
    tags: List[str],
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """为主机添加标签"""
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )

    # 获取现有标签
    existing_tags = host.tags or []

    # 添加新标签（去重）
    new_tags = list(set(existing_tags + tags))

    # 更新主机标签
    await HostCRUD.update_tags(db, host_id, new_tags)

    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_tags_added", "host", str(host_id),
        {"added_tags": tags, "all_tags": new_tags}, request.client.host
    )

    return {"message": "标签添加成功", "tags": new_tags}


@router.delete("/{host_id}/tags")
async def remove_host_tags(
    host_id: int,
    tags: List[str],
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """移除主机标签"""
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )

    # 获取现有标签
    existing_tags = host.tags or []

    # 移除指定标签
    new_tags = [tag for tag in existing_tags if tag not in tags]

    # 更新主机标签
    await HostCRUD.update_tags(db, host_id, new_tags)

    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_tags_removed", "host", str(host_id),
        {"removed_tags": tags, "remaining_tags": new_tags}, request.client.host
    )

    return {"message": "标签移除成功", "tags": new_tags}


@router.post("/batch-tags")
async def batch_update_tags(
    operation: str,  # "add", "remove", "replace"
    host_ids: List[int],
    tags: List[str],
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """批量更新主机标签"""
    if not host_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请选择至少一个主机"
        )

    if not tags:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请提供至少一个标签"
        )

    # 获取主机列表
    hosts = await HostCRUD.get_by_ids(db, host_ids)
    if not hosts:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到指定的主机"
        )

    results = []

    for host in hosts:
        existing_tags = host.tags or []

        if operation == "add":
            new_tags = list(set(existing_tags + tags))
        elif operation == "remove":
            new_tags = [tag for tag in existing_tags if tag not in tags]
        elif operation == "replace":
            new_tags = tags
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的操作类型"
            )

        await HostCRUD.update_tags(db, host.id, new_tags)
        results.append({
            "host_id": host.id,
            "host_name": host.name,
            "old_tags": existing_tags,
            "new_tags": new_tags
        })

    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, f"batch_tags_{operation}", "host", None,
        {
            "operation": operation,
            "host_ids": host_ids,
            "tags": tags,
            "affected_count": len(hosts)
        },
        request.client.host
    )

    return {
        "message": f"批量标签{operation}完成，影响 {len(hosts)} 个主机",
        "results": results
    }


@router.get("/statistics")
async def get_host_statistics(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取主机统计信息"""
    stats = await HostCRUD.get_statistics(db)
    return stats
