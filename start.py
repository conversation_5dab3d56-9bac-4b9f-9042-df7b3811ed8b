#!/usr/bin/env python3
"""
快速启动脚本 - Linux Management Console 2.0
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        sys.exit(1)

def create_directories():
    """创建必要的目录"""
    directories = ["uploads", "logs", "static", "templates"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ 目录创建完成")

def create_env_file():
    """创建环境配置文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建 .env 配置文件...")
        with open(".env", "w", encoding="utf-8") as f:
            f.write("""# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./app.db

# Redis配置 (用于Celery，可选)
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=Linux Management Console
APP_VERSION=2.0.0
DEBUG=True

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=104857600

# SSH配置
SSH_TIMEOUT=30
SSH_CONNECT_TIMEOUT=10
MAX_SSH_CONNECTIONS=50

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
""")
        print("✅ .env 文件创建成功")
    else:
        print("✅ .env 文件已存在")

def init_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    try:
        subprocess.run([sys.executable, "init_db.py"], check=True)
        print("✅ 数据库初始化完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)

def start_application():
    """启动应用"""
    # 检查是否需要初始化数据库
    if not Path("app.db").exists() and not Path(".env").exists():
        print("🔍 检测到首次运行，正在初始化...")
        create_env_file()
        init_database()

    print("🚀 启动 Linux Management Console 2.0...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("👤 默认账户: admin / admin123")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 50)

    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    print("=" * 60)
    print("🐧 Linux Management Console 2.0 - FastAPI版本")
    print("=" * 60)
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    install_dependencies()
    
    # 创建目录
    create_directories()
    
    # 创建配置文件
    create_env_file()
    
    # 启动应用
    start_application()

if __name__ == "__main__":
    main()
