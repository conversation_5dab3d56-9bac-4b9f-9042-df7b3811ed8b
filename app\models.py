"""
数据库模型定义
"""
from datetime import datetime
from typing import List, Optional
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from app.database import Base


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"  # 超级管理员
    ADMIN = "admin"              # 管理员
    OPERATOR = "operator"        # 操作员
    VIEWER = "viewer"            # 查看者


class HostGroup(Base):
    """主机分组模型"""
    __tablename__ = "host_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), default="#007bff")  # 分组颜色
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    hosts = relationship("Host", back_populates="group")
    creator = relationship("User", back_populates="created_groups")


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    full_name = Column(String(100), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default=UserRole.VIEWER)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    created_groups = relationship("HostGroup", back_populates="creator")
    created_hosts = relationship("Host", back_populates="creator")
    created_presets = relationship("Preset", back_populates="creator")
    audit_logs = relationship("AuditLog", back_populates="user")


class Host(Base):
    """主机模型"""
    __tablename__ = "hosts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    ip = Column(String(45), nullable=False)  # 支持IPv6
    port = Column(Integer, default=22)
    username = Column(String(50), nullable=False)
    password = Column(String(255), nullable=True)  # 加密存储
    private_key_path = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True)  # 主机标签
    group_id = Column(Integer, ForeignKey("host_groups.id"), nullable=True)
    is_active = Column(Boolean, default=True)
    last_connected = Column(DateTime, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    group = relationship("HostGroup", back_populates="hosts")
    creator = relationship("User", back_populates="created_hosts")


class Preset(Base):
    """预设操作模型"""
    __tablename__ = "presets"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    type = Column(String(20), nullable=False)  # 'file' or 'command'
    description = Column(Text, nullable=True)

    # 文件分发相关字段
    local_file = Column(String(255), nullable=True)
    remote_path = Column(String(255), nullable=True)

    # 命令执行相关字段
    command = Column(Text, nullable=True)
    use_sudo = Column(Boolean, default=False)

    # 目标主机列表 (JSON格式存储)
    host_ids = Column(JSON, nullable=True)

    # 元数据
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    creator = relationship("User", back_populates="created_presets")


class TaskLog(Base):
    """任务执行日志模型"""
    __tablename__ = "task_logs"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), nullable=False, index=True)  # Celery任务ID
    task_type = Column(String(20), nullable=False)  # 'file_distribution' or 'command_execution'
    host_id = Column(Integer, nullable=False)
    host_name = Column(String(100), nullable=False)

    # 任务状态和结果
    status = Column(String(20), nullable=False)  # 'pending', 'running', 'success', 'failed'
    output = Column(Text, nullable=True)
    error = Column(Text, nullable=True)

    # 时间戳
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())


class AuditLog(Base):
    """操作审计日志模型"""
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action = Column(String(50), nullable=False)  # 操作类型
    resource_type = Column(String(50), nullable=False)  # 资源类型
    resource_id = Column(String(50), nullable=True)  # 资源ID
    details = Column(JSON, nullable=True)  # 操作详情
    ip_address = Column(String(45), nullable=True)  # 客户端IP
    user_agent = Column(String(500), nullable=True)  # 用户代理
    created_at = Column(DateTime, default=func.now())

    # 关系
    user = relationship("User", back_populates="audit_logs")


class ScriptTemplate(Base):
    """脚本模板模型"""
    __tablename__ = "script_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=False)  # 脚本内容
    parameters = Column(JSON, nullable=True)  # 参数定义
    category = Column(String(50), nullable=True)  # 脚本分类
    version = Column(String(20), default="1.0.0")
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class ScheduledTask(Base):
    """定时任务模型"""
    __tablename__ = "scheduled_tasks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    task_type = Column(String(20), nullable=False)  # 'command', 'file', 'script'
    task_config = Column(JSON, nullable=False)  # 任务配置
    cron_expression = Column(String(100), nullable=False)  # Cron表达式
    host_ids = Column(JSON, nullable=True)  # 目标主机
    is_active = Column(Boolean, default=True)
    last_run = Column(DateTime, nullable=True)
    next_run = Column(DateTime, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class SystemMonitor(Base):
    """系统监控数据模型"""
    __tablename__ = "system_monitors"

    id = Column(Integer, primary_key=True, index=True)
    host_id = Column(Integer, ForeignKey("hosts.id"), nullable=False)
    metric_type = Column(String(50), nullable=False)  # cpu, memory, disk, network
    metric_data = Column(JSON, nullable=False)  # 监控数据
    timestamp = Column(DateTime, default=func.now())

    # 索引
    __table_args__ = (
        {"mysql_engine": "InnoDB"},
    )


# Pydantic模型用于API序列化

# 用户相关模型
class UserBase(BaseModel):
    username: str
    email: str
    full_name: Optional[str] = None
    role: UserRole = UserRole.VIEWER


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    id: int
    is_active: bool
    is_verified: bool
    last_login: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    username: str
    password: str


class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse


# 主机分组模型
class HostGroupBase(BaseModel):
    name: str
    description: Optional[str] = None
    color: str = "#007bff"


class HostGroupCreate(HostGroupBase):
    pass


class HostGroupUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None


class HostGroupResponse(HostGroupBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class HostBase(BaseModel):
    name: str
    ip: str
    port: int = 22
    username: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    group_id: Optional[int] = None


class HostCreate(HostBase):
    password: Optional[str] = None
    private_key_path: Optional[str] = None


class HostUpdate(BaseModel):
    name: Optional[str] = None
    ip: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    private_key_path: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    group_id: Optional[int] = None
    is_active: Optional[bool] = None


class HostResponse(HostBase):
    id: int
    is_active: bool
    last_connected: Optional[datetime] = None
    group: Optional[HostGroupResponse] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PresetBase(BaseModel):
    name: str
    type: str  # 'file' or 'command'
    description: Optional[str] = None
    host_ids: List[int]


class PresetCreate(PresetBase):
    # 文件分发字段
    local_file: Optional[str] = None
    remote_path: Optional[str] = None
    
    # 命令执行字段
    command: Optional[str] = None
    use_sudo: bool = False


class PresetUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    local_file: Optional[str] = None
    remote_path: Optional[str] = None
    command: Optional[str] = None
    use_sudo: Optional[bool] = None
    host_ids: Optional[List[int]] = None
    is_active: Optional[bool] = None


class PresetResponse(PresetBase):
    id: int
    local_file: Optional[str] = None
    remote_path: Optional[str] = None
    command: Optional[str] = None
    use_sudo: bool = False
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TaskLogResponse(BaseModel):
    id: int
    task_id: str
    task_type: str
    host_id: int
    host_name: str
    status: str
    output: Optional[str] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


# 请求/响应模型
class FileUploadResponse(BaseModel):
    filename: str
    original_filename: str
    file_path: str
    file_size: int


class CommandExecuteRequest(BaseModel):
    command: str
    host_ids: List[int]
    use_sudo: bool = False


class FileDistributeRequest(BaseModel):
    filename: str
    remote_path: str
    host_ids: List[int]


class TaskResult(BaseModel):
    host_id: int
    host_name: str
    status: str
    output: Optional[str] = None
    error: Optional[str] = None


class BatchOperationResponse(BaseModel):
    task_id: str
    message: str
    total_hosts: int
    results: List[TaskResult] = []


# 脚本模板模型
class ScriptTemplateBase(BaseModel):
    name: str
    description: Optional[str] = None
    content: str
    parameters: Optional[dict] = None
    category: Optional[str] = None


class ScriptTemplateCreate(ScriptTemplateBase):
    pass


class ScriptTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    parameters: Optional[dict] = None
    category: Optional[str] = None
    version: Optional[str] = None
    is_active: Optional[bool] = None


class ScriptTemplateResponse(ScriptTemplateBase):
    id: int
    version: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 定时任务模型
class ScheduledTaskBase(BaseModel):
    name: str
    description: Optional[str] = None
    task_type: str
    task_config: dict
    cron_expression: str
    host_ids: List[int]


class ScheduledTaskCreate(ScheduledTaskBase):
    pass


class ScheduledTaskUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    task_config: Optional[dict] = None
    cron_expression: Optional[str] = None
    host_ids: Optional[List[int]] = None
    is_active: Optional[bool] = None


class ScheduledTaskResponse(ScheduledTaskBase):
    id: int
    is_active: bool
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 审计日志模型
class AuditLogResponse(BaseModel):
    id: int
    user_id: int
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    details: Optional[dict] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    user: Optional[UserResponse] = None

    class Config:
        from_attributes = True


# 系统监控模型
class SystemMonitorResponse(BaseModel):
    id: int
    host_id: int
    metric_type: str
    metric_data: dict
    timestamp: datetime

    class Config:
        from_attributes = True


# WebSocket消息模型
class WebSocketMessage(BaseModel):
    type: str  # 'task_update', 'log_stream', 'system_alert'
    data: dict
    timestamp: datetime = None

    def __init__(self, **data):
        if data.get('timestamp') is None:
            data['timestamp'] = datetime.utcnow()
        super().__init__(**data)
