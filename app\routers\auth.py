"""
用户认证API路由
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.database import get_db_session
from app.models import (
    User, UserCreate, UserUpdate, UserResponse, UserLogin, Token,
    AuditLog, AuditLogResponse
)
from app.auth import (
    AuthManager, authenticate_user, get_current_user, get_current_active_user,
    require_admin, get_user_by_username
)
from app.crud import UserCRUD, AuditLogCRUD
from app.config import settings

router = APIRouter(prefix="/api/auth", tags=["authentication"])


@router.post("/login", response_model=Token)
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db_session)
):
    """用户登录"""
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        # 记录登录失败
        await AuditLogCRUD.create(
            db, None, "login_failed", "user", form_data.username,
            {"reason": "invalid_credentials"}, request.client.host
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = AuthManager.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    # 更新最后登录时间
    await UserCRUD.update_last_login(db, user.id)
    
    # 记录登录成功
    await AuditLogCRUD.create(
        db, user.id, "login_success", "user", str(user.id),
        {"username": user.username}, request.client.host
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60,
        "user": user
    }


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_admin)  # 只有管理员可以创建用户
):
    """用户注册（管理员操作）"""
    # 检查用户名是否已存在
    existing_user = await get_user_by_username(db, user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    existing_email = await UserCRUD.get_by_email(db, user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建用户
    user = await UserCRUD.create(db, user_data)
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "user_created", "user", str(user.id),
        {"username": user.username, "role": user.role}, request.client.host
    )
    
    return user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db_session)
):
    """更新当前用户信息"""
    # 普通用户只能更新部分字段
    allowed_fields = {"email", "full_name"}
    update_data = {k: v for k, v in user_update.dict(exclude_unset=True).items() 
                   if k in allowed_fields}
    
    if not update_data:
        return current_user
    
    user = await UserCRUD.update(db, current_user.id, UserUpdate(**update_data))
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "profile_updated", "user", str(current_user.id),
        update_data, request.client.host
    )
    
    return user


@router.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db_session)
):
    """获取用户列表（管理员操作）"""
    users = await UserCRUD.get_all(db, skip=skip, limit=limit)
    return users


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db_session)
):
    """获取用户信息（管理员操作）"""
    user = await UserCRUD.get(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db_session)
):
    """更新用户信息（管理员操作）"""
    user = await UserCRUD.update(db, user_id, user_update)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "user_updated", "user", str(user_id),
        user_update.dict(exclude_unset=True), request.client.host
    )
    
    return user


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db_session)
):
    """删除用户（管理员操作）"""
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    success = await UserCRUD.delete(db, user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "user_deleted", "user", str(user_id),
        {}, request.client.host
    )
    
    return {"message": "用户删除成功"}


@router.get("/audit-logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    skip: int = 0,
    limit: int = 100,
    action: str = None,
    resource_type: str = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db_session)
):
    """获取审计日志（管理员操作）"""
    logs = await AuditLogCRUD.get_logs(
        db, skip=skip, limit=limit, action=action, resource_type=resource_type
    )
    return logs


@router.post("/change-password")
async def change_password(
    current_password: str,
    new_password: str,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db_session)
):
    """修改密码"""
    # 验证当前密码
    if not AuthManager.verify_password(current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 更新密码
    hashed_password = AuthManager.get_password_hash(new_password)
    await UserCRUD.update_password(db, current_user.id, hashed_password)
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "password_changed", "user", str(current_user.id),
        {}, request.client.host
    )
    
    return {"message": "密码修改成功"}
