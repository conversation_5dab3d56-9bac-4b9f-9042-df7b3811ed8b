"""
应用配置模块
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "Linux Management Console"
    app_version: str = "2.0.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str = "sqlite+aiosqlite:///./app.db"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # 安全配置
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 104857600  # 100MB
    
    # SSH配置
    ssh_timeout: int = 30
    ssh_connect_timeout: int = 10
    max_ssh_connections: int = 50
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
