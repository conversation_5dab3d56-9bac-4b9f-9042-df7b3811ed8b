"""
应用配置模块 - 简化版
"""
import os


class Settings:
    """应用配置类 - 不依赖pydantic"""

    def __init__(self):
        # 应用基础配置
        self.app_name = os.getenv("APP_NAME", "Linux Management Console")
        self.app_version = os.getenv("APP_VERSION", "2.0.0")
        self.debug = os.getenv("DEBUG", "True").lower() in ("true", "1", "yes")

        # 数据库配置
        self.database_url = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./app.db")

        # Redis配置
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

        # 安全配置
        self.secret_key = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
        self.algorithm = os.getenv("ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

        # 文件上传配置
        self.upload_dir = os.getenv("UPLOAD_DIR", "./uploads")
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", "104857600"))  # 100MB

        # SSH配置
        self.ssh_timeout = int(os.getenv("SSH_TIMEOUT", "30"))
        self.ssh_connect_timeout = int(os.getenv("SSH_CONNECT_TIMEOUT", "10"))
        self.max_ssh_connections = int(os.getenv("MAX_SSH_CONNECTIONS", "50"))

        # 日志配置
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "./logs/app.log")


# 创建全局设置实例
settings = Settings()


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
