#!/usr/bin/env python3
"""
最简单的测试版本 - 只测试FastAPI基础功能
"""
import os
import sys

# 确保目录存在
os.makedirs("uploads", exist_ok=True)
os.makedirs("logs", exist_ok=True)

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse
    print("✅ FastAPI 导入成功")
except ImportError as e:
    print(f"❌ FastAPI 导入失败: {e}")
    print("请安装: pip install fastapi")
    sys.exit(1)

try:
    import uvicorn
    print("✅ Uvicorn 导入成功")
except ImportError as e:
    print(f"❌ Uvicorn 导入失败: {e}")
    print("请安装: pip install uvicorn")
    sys.exit(1)

# 创建FastAPI应用
app = FastAPI(
    title="Linux Management Console 2.0 - 简单测试版",
    description="最简单的测试版本，验证FastAPI基础功能",
    version="2.0.0-simple"
)

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Linux Management Console 2.0 - 简单测试版</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .main-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); }
        </style>
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card main-card">
                        <div class="card-header bg-primary text-white text-center">
                            <h1><i class="bi bi-terminal me-2"></i>Linux Management Console 2.0</h1>
                            <p class="mb-0">简单测试版 - FastAPI基础功能验证</p>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success text-center">
                                <h4><i class="bi bi-check-circle-fill me-2"></i>🎉 测试成功！</h4>
                                <p class="mb-0">FastAPI应用正常运行，基础框架没有问题。</p>
                            </div>
                            
                            <div class="row text-center mb-4">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                            <h6 class="mt-2">FastAPI</h6>
                                            <small>框架正常</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                            <h6 class="mt-2">Uvicorn</h6>
                                            <small>服务器正常</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                            <h6 class="mt-2">Python</h6>
                                            <small>环境正常</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <h5><i class="bi bi-link-45deg me-1"></i>测试链接</h5>
                            <div class="list-group mb-4">
                                <a href="/docs" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-file-text me-2"></i>API文档 (Swagger UI)
                                </a>
                                <a href="/redoc" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-file-earmark-text me-2"></i>API文档 (ReDoc)
                                </a>
                                <a href="/health" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-heart-pulse me-2"></i>健康检查
                                </a>
                                <a href="/test" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="bi bi-gear me-2"></i>API测试
                                </a>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-1"></i>下一步</h6>
                                <p class="mb-2">基础功能测试通过！现在可以：</p>
                                <ol class="mb-0">
                                    <li>安装更多依赖: <code>pip install -r requirements-minimal.txt</code></li>
                                    <li>运行完整测试: <code>python test_app.py</code></li>
                                    <li>启动完整版本: <code>python start.py</code></li>
                                </ol>
                            </div>
                        </div>
                        <div class="card-footer text-center text-muted">
                            <small>如果看到这个页面，说明FastAPI环境配置正确 ✅</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "简单测试版运行正常",
        "version": "2.0.0-simple",
        "framework": "FastAPI",
        "python_version": sys.version,
        "dependencies": {
            "fastapi": "✅ 正常",
            "uvicorn": "✅ 正常"
        }
    }

@app.get("/test")
async def test_api():
    """API功能测试"""
    return {
        "message": "API测试成功！",
        "status": "success",
        "features_working": [
            "HTTP路由",
            "JSON响应", 
            "异步处理",
            "自动文档生成"
        ],
        "next_steps": [
            "安装数据库依赖",
            "添加SSH功能",
            "集成前端界面",
            "添加用户认证"
        ]
    }

@app.get("/info")
async def get_info():
    """系统信息"""
    import platform
    return {
        "system": {
            "platform": platform.system(),
            "python_version": platform.python_version(),
            "architecture": platform.architecture()[0]
        },
        "app": {
            "name": "Linux Management Console 2.0",
            "version": "2.0.0-simple",
            "status": "running"
        },
        "directories": {
            "uploads": os.path.exists("uploads"),
            "logs": os.path.exists("logs")
        }
    }

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Linux Management Console 2.0 - 简单测试版")
    print("=" * 60)
    print(f"✅ Python版本: {sys.version}")
    print("✅ 目录检查完成")
    print("✅ FastAPI导入成功")
    print("✅ Uvicorn导入成功")
    print("-" * 60)
    print("🌐 启动Web服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🏥 健康检查: http://localhost:8000/health")
    print("⏹️  按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        print("✅ 基础功能测试完成！")

if __name__ == "__main__":
    main()
