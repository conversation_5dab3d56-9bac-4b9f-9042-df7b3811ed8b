"""
数据库CRUD操作
"""
from typing import List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.models import (
    Host, Preset, TaskLog, User, HostGroup, AuditLog, ScriptTemplate, ScheduledTask,
    HostCreate, HostUpdate, PresetCreate, PresetUpdate, UserCreate, UserUpdate,
    HostGroupCreate, HostGroupUpdate
)
from app.auth import AuthManager


class HostCRUD:
    """主机CRUD操作"""

    @staticmethod
    async def create(db: AsyncSession, host_data: HostCreate, created_by: int = None) -> Host:
        """创建主机"""
        host = Host(**host_data.dict(), created_by=created_by)
        db.add(host)
        await db.commit()
        await db.refresh(host)
        return host
    
    @staticmethod
    async def get(db: AsyncSession, host_id: int) -> Optional[Host]:
        """获取单个主机"""
        result = await db.execute(select(Host).where(Host.id == host_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_all(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        group_id: Optional[int] = None,
        tags: Optional[List[str]] = None,
        search: Optional[str] = None
    ) -> List[Host]:
        """获取所有主机"""
        query = select(Host).options(selectinload(Host.group)).where(Host.is_active == True)

        # 按分组筛选
        if group_id is not None:
            query = query.where(Host.group_id == group_id)

        # 按标签筛选
        if tags:
            for tag in tags:
                query = query.where(Host.tags.contains([tag]))

        # 搜索功能
        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (Host.name.ilike(search_pattern)) |
                (Host.ip.ilike(search_pattern)) |
                (Host.description.ilike(search_pattern))
            )

        query = query.offset(skip).limit(limit).order_by(Host.created_at.desc())

        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_by_ids(db: AsyncSession, host_ids: List[int]) -> List[Host]:
        """根据ID列表获取主机"""
        result = await db.execute(
            select(Host)
            .where(Host.id.in_(host_ids))
            .where(Host.is_active == True)
        )
        return result.scalars().all()
    
    @staticmethod
    async def update(db: AsyncSession, host_id: int, host_data: HostUpdate) -> Optional[Host]:
        """更新主机"""
        host = await HostCRUD.get(db, host_id)
        if not host:
            return None
        
        update_data = host_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(host, field, value)
        
        await db.commit()
        await db.refresh(host)
        return host
    
    @staticmethod
    async def delete(db: AsyncSession, host_id: int) -> bool:
        """删除主机（软删除）"""
        host = await HostCRUD.get(db, host_id)
        if not host:
            return False
        
        host.is_active = False
        await db.commit()
        return True
    
    @staticmethod
    async def update_last_connected(db: AsyncSession, host_id: int):
        """更新最后连接时间"""
        from datetime import datetime
        host = await HostCRUD.get(db, host_id)
        if host:
            host.last_connected = datetime.utcnow()
            await db.commit()

    @staticmethod
    async def update_tags(db: AsyncSession, host_id: int, tags: List[str]):
        """更新主机标签"""
        host = await HostCRUD.get(db, host_id)
        if host:
            host.tags = tags
            await db.commit()

    @staticmethod
    async def get_all_tags(db: AsyncSession) -> List[str]:
        """获取所有主机标签"""
        result = await db.execute(select(Host.tags).where(Host.is_active == True))
        all_tags = set()
        for tags in result.scalars():
            if tags:
                all_tags.update(tags)
        return sorted(list(all_tags))

    @staticmethod
    async def get_statistics(db: AsyncSession) -> dict:
        """获取主机统计信息"""
        # 总主机数
        total_result = await db.execute(
            select(func.count(Host.id)).where(Host.is_active == True)
        )
        total_hosts = total_result.scalar()

        # 在线主机数（最近5分钟有连接的）
        from datetime import datetime, timedelta
        recent_time = datetime.utcnow() - timedelta(minutes=5)
        online_result = await db.execute(
            select(func.count(Host.id))
            .where(Host.is_active == True)
            .where(Host.last_connected >= recent_time)
        )
        online_hosts = online_result.scalar()

        # 按分组统计
        group_result = await db.execute(
            select(HostGroup.name, func.count(Host.id))
            .outerjoin(Host, HostGroup.id == Host.group_id)
            .where(Host.is_active == True)
            .group_by(HostGroup.id, HostGroup.name)
        )
        group_stats = dict(group_result.all())

        # 未分组主机数
        ungrouped_result = await db.execute(
            select(func.count(Host.id))
            .where(Host.is_active == True)
            .where(Host.group_id.is_(None))
        )
        ungrouped_hosts = ungrouped_result.scalar()
        if ungrouped_hosts > 0:
            group_stats["未分组"] = ungrouped_hosts

        return {
            "total_hosts": total_hosts,
            "online_hosts": online_hosts,
            "offline_hosts": total_hosts - online_hosts,
            "group_statistics": group_stats
        }


class PresetCRUD:
    """预设操作CRUD"""
    
    @staticmethod
    async def create(db: AsyncSession, preset_data: PresetCreate) -> Preset:
        """创建预设"""
        preset = Preset(**preset_data.dict())
        db.add(preset)
        await db.commit()
        await db.refresh(preset)
        return preset
    
    @staticmethod
    async def get(db: AsyncSession, preset_id: int) -> Optional[Preset]:
        """获取单个预设"""
        result = await db.execute(select(Preset).where(Preset.id == preset_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_all(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Preset]:
        """获取所有预设"""
        result = await db.execute(
            select(Preset)
            .where(Preset.is_active == True)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    @staticmethod
    async def update(db: AsyncSession, preset_id: int, preset_data: PresetUpdate) -> Optional[Preset]:
        """更新预设"""
        preset = await PresetCRUD.get(db, preset_id)
        if not preset:
            return None
        
        update_data = preset_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(preset, field, value)
        
        await db.commit()
        await db.refresh(preset)
        return preset
    
    @staticmethod
    async def delete(db: AsyncSession, preset_id: int) -> bool:
        """删除预设（软删除）"""
        preset = await PresetCRUD.get(db, preset_id)
        if not preset:
            return False
        
        preset.is_active = False
        await db.commit()
        return True


class TaskLogCRUD:
    """任务日志CRUD"""
    
    @staticmethod
    async def create(
        db: AsyncSession,
        task_id: str,
        task_type: str,
        host_id: int,
        host_name: str,
        status: str = "pending"
    ) -> TaskLog:
        """创建任务日志"""
        task_log = TaskLog(
            task_id=task_id,
            task_type=task_type,
            host_id=host_id,
            host_name=host_name,
            status=status
        )
        db.add(task_log)
        await db.commit()
        await db.refresh(task_log)
        return task_log
    
    @staticmethod
    async def update_status(
        db: AsyncSession,
        task_id: str,
        host_id: int,
        status: str,
        output: str = None,
        error: str = None
    ):
        """更新任务状态"""
        from datetime import datetime
        
        result = await db.execute(
            select(TaskLog)
            .where(TaskLog.task_id == task_id)
            .where(TaskLog.host_id == host_id)
        )
        task_log = result.scalar_one_or_none()
        
        if task_log:
            task_log.status = status
            if output is not None:
                task_log.output = output
            if error is not None:
                task_log.error = error
            
            if status == "running" and not task_log.started_at:
                task_log.started_at = datetime.utcnow()
            elif status in ["success", "failed"]:
                task_log.completed_at = datetime.utcnow()
            
            await db.commit()
    
    @staticmethod
    async def get_by_task_id(db: AsyncSession, task_id: str) -> List[TaskLog]:
        """根据任务ID获取日志"""
        result = await db.execute(
            select(TaskLog).where(TaskLog.task_id == task_id)
        )
        return result.scalars().all()

    @staticmethod
    async def get_recent_logs(db: AsyncSession, limit: int = 50) -> List[TaskLog]:
        """获取最近的任务日志"""
        result = await db.execute(
            select(TaskLog)
            .order_by(TaskLog.created_at.desc())
            .limit(limit)
        )
        return result.scalars().all()


class UserCRUD:
    """用户CRUD操作"""

    @staticmethod
    async def create(db: AsyncSession, user_data: UserCreate) -> User:
        """创建用户"""
        hashed_password = AuthManager.get_password_hash(user_data.password)
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            hashed_password=hashed_password,
            role=user_data.role
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user

    @staticmethod
    async def get(db: AsyncSession, user_id: int) -> Optional[User]:
        """获取单个用户"""
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_email(db: AsyncSession, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()

    @staticmethod
    async def get_all(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
        """获取所有用户"""
        result = await db.execute(
            select(User)
            .offset(skip)
            .limit(limit)
            .order_by(User.created_at.desc())
        )
        return result.scalars().all()

    @staticmethod
    async def update(db: AsyncSession, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """更新用户"""
        user = await UserCRUD.get(db, user_id)
        if not user:
            return None

        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)

        await db.commit()
        await db.refresh(user)
        return user

    @staticmethod
    async def update_password(db: AsyncSession, user_id: int, hashed_password: str):
        """更新用户密码"""
        user = await UserCRUD.get(db, user_id)
        if user:
            user.hashed_password = hashed_password
            await db.commit()

    @staticmethod
    async def update_last_login(db: AsyncSession, user_id: int):
        """更新最后登录时间"""
        user = await UserCRUD.get(db, user_id)
        if user:
            user.last_login = datetime.utcnow()
            await db.commit()

    @staticmethod
    async def delete(db: AsyncSession, user_id: int) -> bool:
        """删除用户（软删除）"""
        user = await UserCRUD.get(db, user_id)
        if not user:
            return False

        user.is_active = False
        await db.commit()
        return True


class HostGroupCRUD:
    """主机分组CRUD操作"""

    @staticmethod
    async def create(db: AsyncSession, group_data: HostGroupCreate, created_by: int = None) -> HostGroup:
        """创建主机分组"""
        group = HostGroup(**group_data.dict(), created_by=created_by)
        db.add(group)
        await db.commit()
        await db.refresh(group)
        return group

    @staticmethod
    async def get(db: AsyncSession, group_id: int) -> Optional[HostGroup]:
        """获取单个主机分组"""
        result = await db.execute(
            select(HostGroup)
            .options(selectinload(HostGroup.hosts))
            .where(HostGroup.id == group_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_all(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[HostGroup]:
        """获取所有主机分组"""
        result = await db.execute(
            select(HostGroup)
            .options(selectinload(HostGroup.hosts))
            .offset(skip)
            .limit(limit)
            .order_by(HostGroup.created_at.desc())
        )
        return result.scalars().all()

    @staticmethod
    async def update(db: AsyncSession, group_id: int, group_data: HostGroupUpdate) -> Optional[HostGroup]:
        """更新主机分组"""
        group = await HostGroupCRUD.get(db, group_id)
        if not group:
            return None

        update_data = group_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(group, field, value)

        await db.commit()
        await db.refresh(group)
        return group

    @staticmethod
    async def delete(db: AsyncSession, group_id: int) -> bool:
        """删除主机分组"""
        group = await HostGroupCRUD.get(db, group_id)
        if not group:
            return False

        # 将分组下的主机移到未分组
        for host in group.hosts:
            host.group_id = None

        await db.delete(group)
        await db.commit()
        return True


class AuditLogCRUD:
    """审计日志CRUD操作"""

    @staticmethod
    async def create(
        db: AsyncSession,
        user_id: Optional[int],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuditLog:
        """创建审计日志"""
        log = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.add(log)
        await db.commit()
        await db.refresh(log)
        return log

    @staticmethod
    async def get_logs(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> List[AuditLog]:
        """获取审计日志"""
        query = select(AuditLog).options(selectinload(AuditLog.user))

        if action:
            query = query.where(AuditLog.action == action)
        if resource_type:
            query = query.where(AuditLog.resource_type == resource_type)
        if user_id:
            query = query.where(AuditLog.user_id == user_id)

        query = query.order_by(AuditLog.created_at.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()


class ScriptTemplateCRUD:
    """脚本模板CRUD操作"""

    @staticmethod
    async def create(db: AsyncSession, template_data, created_by: int = None):
        """创建脚本模板"""
        template = ScriptTemplate(**template_data.dict(), created_by=created_by)
        db.add(template)
        await db.commit()
        await db.refresh(template)
        return template

    @staticmethod
    async def get(db: AsyncSession, template_id: int):
        """获取单个脚本模板"""
        result = await db.execute(select(ScriptTemplate).where(ScriptTemplate.id == template_id))
        return result.scalar_one_or_none()

    @staticmethod
    async def get_all(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        category: Optional[str] = None,
        search: Optional[str] = None
    ):
        """获取所有脚本模板"""
        query = select(ScriptTemplate).where(ScriptTemplate.is_active == True)

        if category:
            query = query.where(ScriptTemplate.category == category)

        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (ScriptTemplate.name.ilike(search_pattern)) |
                (ScriptTemplate.description.ilike(search_pattern))
            )

        query = query.offset(skip).limit(limit).order_by(ScriptTemplate.created_at.desc())

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_categories(db: AsyncSession) -> List[str]:
        """获取所有模板分类"""
        result = await db.execute(
            select(ScriptTemplate.category)
            .where(ScriptTemplate.is_active == True)
            .where(ScriptTemplate.category.isnot(None))
            .distinct()
        )
        return [category for category in result.scalars() if category]

    @staticmethod
    async def update(db: AsyncSession, template_id: int, template_data):
        """更新脚本模板"""
        template = await ScriptTemplateCRUD.get(db, template_id)
        if not template:
            return None

        update_data = template_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(template, field, value)

        await db.commit()
        await db.refresh(template)
        return template

    @staticmethod
    async def delete(db: AsyncSession, template_id: int) -> bool:
        """删除脚本模板（软删除）"""
        template = await ScriptTemplateCRUD.get(db, template_id)
        if not template:
            return False

        template.is_active = False
        await db.commit()
        return True
