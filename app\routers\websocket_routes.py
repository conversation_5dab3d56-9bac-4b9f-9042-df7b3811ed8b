"""
WebSocket路由
"""
import json
import uuid
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.websocket import manager, handle_websocket_message
from app.database import AsyncSessionLocal
from app.auth import AuthManager
from app.crud import UserCRUD

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_user_from_token(token: str):
    """从token获取用户信息"""
    if not token:
        return None
    
    payload = AuthManager.verify_token(token)
    if not payload:
        return None
    
    username = payload.get("sub")
    if not username:
        return None
    
    async with AsyncSessionLocal() as db:
        from app.auth import get_user_by_username
        user = await get_user_by_username(db, username)
        return user


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(None, description="JWT认证令牌")
):
    """WebSocket连接端点"""
    connection_id = str(uuid.uuid4())
    
    try:
        # 验证用户身份
        user = await get_user_from_token(token)
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="认证失败")
            return
        
        if not user.is_active:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="用户账户已被禁用")
            return
        
        # 建立连接
        await manager.connect(websocket, user, connection_id)
        
        # 监听消息
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理消息
                await handle_websocket_message(websocket, user, message_data)
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "data": {"message": "无效的JSON格式"}
                }))
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "data": {"message": "消息处理失败"}
                }))
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {connection_id}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        manager.disconnect(connection_id)


@router.websocket("/ws/logs/{host_id}")
async def websocket_logs_endpoint(
    websocket: WebSocket,
    host_id: int,
    token: str = Query(None, description="JWT认证令牌")
):
    """主机日志流WebSocket端点"""
    connection_id = str(uuid.uuid4())
    
    try:
        # 验证用户身份
        user = await get_user_from_token(token)
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="认证失败")
            return
        
        # 建立连接
        await manager.connect(websocket, user, connection_id)
        
        # 自动订阅主机日志频道
        manager.subscribe_to_channel(user.id, f"host:{host_id}:logs")
        
        # 发送订阅确认
        await websocket.send_text(json.dumps({
            "type": "log_subscription_confirmed",
            "data": {"host_id": host_id}
        }))
        
        # 保持连接
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理特定的日志相关消息
                if message_data.get("type") == "request_history":
                    # 请求历史日志
                    await websocket.send_text(json.dumps({
                        "type": "log_history",
                        "data": {
                            "host_id": host_id,
                            "message": "历史日志功能待实现"
                        }
                    }))
                else:
                    await handle_websocket_message(websocket, user, message_data)
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "data": {"message": "无效的JSON格式"}
                }))
    
    except WebSocketDisconnect:
        logger.info(f"日志WebSocket连接断开: {connection_id}")
    except Exception as e:
        logger.error(f"日志WebSocket连接异常: {e}")
    finally:
        # 清理连接
        manager.disconnect(connection_id)


@router.websocket("/ws/tasks/{task_id}")
async def websocket_task_endpoint(
    websocket: WebSocket,
    task_id: str,
    token: str = Query(None, description="JWT认证令牌")
):
    """任务状态WebSocket端点"""
    connection_id = str(uuid.uuid4())
    
    try:
        # 验证用户身份
        user = await get_user_from_token(token)
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="认证失败")
            return
        
        # 建立连接
        await manager.connect(websocket, user, connection_id)
        
        # 自动订阅任务频道
        manager.subscribe_to_channel(user.id, f"task:{task_id}")
        
        # 发送订阅确认
        await websocket.send_text(json.dumps({
            "type": "task_subscription_confirmed",
            "data": {"task_id": task_id}
        }))
        
        # 保持连接
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                await handle_websocket_message(websocket, user, message_data)
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "data": {"message": "无效的JSON格式"}
                }))
    
    except WebSocketDisconnect:
        logger.info(f"任务WebSocket连接断开: {connection_id}")
    except Exception as e:
        logger.error(f"任务WebSocket连接异常: {e}")
    finally:
        # 清理连接
        manager.disconnect(connection_id)
