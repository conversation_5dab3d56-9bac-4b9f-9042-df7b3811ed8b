# 安装指南 - Linux Management Console 2.0

## 🚨 解决Pydantic依赖问题

你遇到的错误是因为Pydantic v2版本的变化。让我们一步步解决：

## 🔧 快速解决方案

### 方案1: 最简单测试（推荐）
```bash
# 1. 只安装最基础的依赖
pip install fastapi uvicorn

# 2. 运行简单测试版
python simple_test.py

# 3. 访问 http://localhost:8000
```

### 方案2: 修复Pydantic问题
```bash
# 1. 卸载有问题的pydantic版本
pip uninstall pydantic pydantic-settings -y

# 2. 安装兼容版本
pip install "pydantic<2.0" fastapi uvicorn

# 3. 运行测试
python simple_test.py
```

### 方案3: 使用虚拟环境（最安全）
```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 3. 安装依赖
pip install fastapi uvicorn

# 4. 运行测试
python simple_test.py
```

## 📋 分步安装流程

### 第1步: 基础环境测试
```bash
# 测试Python环境
python --version  # 需要3.8+

# 测试最基础功能
python simple_test.py
```

### 第2步: 安装核心依赖
```bash
# 如果第1步成功，继续安装
pip install sqlalchemy==1.4.53 aiosqlite paramiko
```

### 第3步: 测试数据库功能
```bash
# 运行带数据库的测试
python test_app.py
```

### 第4步: 安装完整功能（可选）
```bash
# 如果前面都成功，安装完整功能
pip install python-jose[cryptography] passlib[bcrypt]
```

## 🐛 常见问题解决

### 问题1: ModuleNotFoundError: No module named 'pydantic_settings'
**解决方案:**
```bash
# 方案A: 降级pydantic
pip install "pydantic<2.0"

# 方案B: 安装pydantic-settings
pip install pydantic-settings

# 方案C: 使用简化版本（推荐）
python simple_test.py
```

### 问题2: 加密库安装失败
**解决方案:**
```bash
# Windows
pip install --upgrade pip setuptools wheel

# Linux
sudo apt-get install build-essential libffi-dev libssl-dev

# Mac
brew install libffi openssl

# 如果还是失败，跳过加密功能
python simple_test.py  # 不需要加密库
```

### 问题3: SQLAlchemy版本冲突
**解决方案:**
```bash
# 使用指定版本
pip install sqlalchemy==1.4.53

# 或者先测试基础功能
python simple_test.py
```

## 🎯 推荐的安装顺序

### 新手用户
1. `python simple_test.py` - 测试基础功能
2. 如果成功，继续安装更多依赖
3. 如果失败，检查Python版本和环境

### 有经验用户
1. 创建虚拟环境
2. 安装最小依赖: `pip install -r requirements-minimal.txt`
3. 运行完整测试: `python test_app.py`

### 开发者
1. 克隆项目到本地
2. 使用虚拟环境
3. 逐步安装依赖并测试

## 📦 依赖说明

### 必需依赖（核心功能）
- `fastapi` - Web框架
- `uvicorn` - ASGI服务器

### 可选依赖（扩展功能）
- `sqlalchemy` - 数据库ORM
- `aiosqlite` - SQLite异步驱动
- `paramiko` - SSH连接
- `python-jose` - JWT认证
- `passlib` - 密码加密

## 🚀 验证安装

运行以下命令验证安装：

```bash
# 1. 基础功能测试
python simple_test.py

# 2. 访问测试页面
# http://localhost:8000

# 3. 检查API文档
# http://localhost:8000/docs

# 4. 健康检查
# http://localhost:8000/health
```

## 🆘 如果还是有问题

1. **检查Python版本**: 必须是3.8或更高
2. **更新pip**: `pip install --upgrade pip`
3. **清理缓存**: `pip cache purge`
4. **重新安装**: 删除虚拟环境重新创建

## 📞 获取帮助

如果以上方法都不行：

1. 运行 `python simple_test.py` 看是否能启动基础版本
2. 检查错误信息，通常是依赖版本冲突
3. 使用虚拟环境隔离依赖
4. 逐个安装依赖包，找出问题所在

记住：**simple_test.py** 是最简单的版本，只需要FastAPI和Uvicorn，如果这个都不能运行，说明是Python环境问题。
