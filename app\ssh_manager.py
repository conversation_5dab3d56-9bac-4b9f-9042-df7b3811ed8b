"""
SSH连接管理模块
提供异步SSH连接池和操作封装
"""
import asyncio
import os
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from contextlib import asynccontextmanager

import paramiko
from paramiko import SSHClient, AutoAddPolicy
from paramiko.ssh_exception import SSHException, AuthenticationException

from app.config import settings
from app.models import Host

logger = logging.getLogger(__name__)


@dataclass
class SSHResult:
    """SSH操作结果"""
    success: bool
    output: str = ""
    error: str = ""
    exit_code: int = 0


class SSHConnectionPool:
    """SSH连接池管理器（简化版）"""

    def __init__(self, max_connections: int = None):
        self.max_connections = max_connections or settings.max_ssh_connections
        self._semaphore = asyncio.Semaphore(self.max_connections)

    def _get_connection_key(self, host: Host) -> str:
        """生成连接键"""
        return f"{host.username}@{host.ip}:{host.port}"

    async def _create_connection(self, host: Host) -> SSHClient:
        """创建SSH连接"""
        client = SSHClient()
        client.set_missing_host_key_policy(AutoAddPolicy())

        try:
            # 在线程池中执行阻塞的SSH连接
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: client.connect(
                    hostname=host.ip,
                    port=host.port,
                    username=host.username,
                    password=host.password,
                    key_filename=host.private_key_path if host.private_key_path and os.path.exists(host.private_key_path) else None,
                    timeout=settings.ssh_connect_timeout,
                    allow_agent=True,
                    look_for_keys=True
                )
            )
            logger.info(f"SSH连接已建立: {self._get_connection_key(host)}")
            return client
        except (SSHException, AuthenticationException, OSError) as e:
            logger.error(f"SSH连接失败 {self._get_connection_key(host)}: {e}")
            client.close()
            raise

    async def get_connection(self, host: Host):
        """获取SSH连接"""
        async with self._semaphore:  # 限制并发连接数
            return await self._create_connection(host)

    async def close_all_connections(self):
        """关闭所有连接（简化版不需要实现）"""
        pass


class SSHManager:
    """SSH操作管理器"""
    
    def __init__(self):
        self.connection_pool = SSHConnectionPool()
    
    async def execute_command(
        self,
        host: Host,
        command: str,
        use_sudo: bool = False,
        timeout: int = None
    ) -> SSHResult:
        """执行远程命令"""
        timeout = timeout or settings.ssh_timeout

        if use_sudo:
            command = f"sudo -S -p '' {command}"

        client = None
        try:
            client = await self.connection_pool.get_connection(host)

            # 在线程池中执行命令
            loop = asyncio.get_event_loop()
            stdin, stdout, stderr = await loop.run_in_executor(
                None,
                lambda: client.exec_command(command, timeout=timeout)
            )

            # 读取输出
            output = await loop.run_in_executor(None, stdout.read)
            error = await loop.run_in_executor(None, stderr.read)
            exit_code = stdout.channel.recv_exit_status()

            return SSHResult(
                success=exit_code == 0,
                output=output.decode('utf-8', errors='ignore'),
                error=error.decode('utf-8', errors='ignore'),
                exit_code=exit_code
            )

        except Exception as e:
            return SSHResult(
                success=False,
                error=f"SSH执行错误: {str(e)}"
            )
        finally:
            if client:
                client.close()
    
    async def upload_file(
        self,
        host: Host,
        local_path: str,
        remote_path: str
    ) -> SSHResult:
        """上传文件到远程主机"""
        client = None
        try:
            client = await self.connection_pool.get_connection(host)

            # 在线程池中执行文件传输
            loop = asyncio.get_event_loop()

            def _upload_file():
                sftp = client.open_sftp()
                try:
                    # 确保远程目录存在
                    remote_dir = os.path.dirname(remote_path)
                    if remote_dir:
                        try:
                            sftp.makedirs(remote_dir)
                        except Exception:
                            pass  # 目录可能已存在

                    # 上传文件
                    sftp.put(local_path, remote_path)
                    return f"文件已上传到 {remote_path}"
                finally:
                    sftp.close()

            output = await loop.run_in_executor(None, _upload_file)

            return SSHResult(
                success=True,
                output=output
            )

        except Exception as e:
            return SSHResult(
                success=False,
                error=f"文件上传失败: {str(e)}"
            )
        finally:
            if client:
                client.close()
    
    async def download_file(
        self,
        host: Host,
        remote_path: str,
        local_path: str
    ) -> SSHResult:
        """从远程主机下载文件"""
        client = None
        try:
            client = await self.connection_pool.get_connection(host)

            # 在线程池中执行文件传输
            loop = asyncio.get_event_loop()

            def _download_file():
                sftp = client.open_sftp()
                try:
                    # 确保本地目录存在
                    local_dir = os.path.dirname(local_path)
                    if local_dir:
                        os.makedirs(local_dir, exist_ok=True)

                    # 下载文件
                    sftp.get(remote_path, local_path)
                    return f"文件已下载到 {local_path}"
                finally:
                    sftp.close()

            output = await loop.run_in_executor(None, _download_file)

            return SSHResult(
                success=True,
                output=output
            )

        except Exception as e:
            return SSHResult(
                success=False,
                error=f"文件下载失败: {str(e)}"
            )
        finally:
            if client:
                client.close()
    
    async def test_connection(self, host: Host) -> SSHResult:
        """测试SSH连接"""
        try:
            result = await self.execute_command(host, "echo 'Connection test successful'")
            if result.success:
                return SSHResult(
                    success=True,
                    output="连接测试成功"
                )
            else:
                return result
        except Exception as e:
            return SSHResult(
                success=False,
                error=f"连接测试失败: {str(e)}"
            )
    
    async def batch_execute_command(
        self, 
        hosts: List[Host], 
        command: str, 
        use_sudo: bool = False,
        timeout: int = None
    ) -> List[Tuple[Host, SSHResult]]:
        """批量执行命令"""
        tasks = [
            self.execute_command(host, command, use_sudo, timeout)
            for host in hosts
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = []
        for host, result in zip(hosts, results):
            if isinstance(result, Exception):
                processed_results.append((
                    host,
                    SSHResult(
                        success=False,
                        error=f"执行异常: {str(result)}"
                    )
                ))
            else:
                processed_results.append((host, result))
        
        return processed_results
    
    async def batch_upload_file(
        self, 
        hosts: List[Host], 
        local_path: str, 
        remote_path: str
    ) -> List[Tuple[Host, SSHResult]]:
        """批量上传文件"""
        tasks = [
            self.upload_file(host, local_path, remote_path)
            for host in hosts
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = []
        for host, result in zip(hosts, results):
            if isinstance(result, Exception):
                processed_results.append((
                    host,
                    SSHResult(
                        success=False,
                        error=f"上传异常: {str(result)}"
                    )
                ))
            else:
                processed_results.append((host, result))
        
        return processed_results
    
    async def close(self):
        """关闭SSH管理器"""
        await self.connection_pool.close_all_connections()


# 全局SSH管理器实例
ssh_manager = SSHManager()


async def get_ssh_manager() -> SSHManager:
    """获取SSH管理器实例"""
    return ssh_manager
