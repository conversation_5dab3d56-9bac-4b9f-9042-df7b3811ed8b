# 故障排除指南

## 🔧 依赖安装问题

### 问题1: SQLAlchemy版本冲突
```
ERROR: Cannot install -r requirements.txt because these package versions have conflicting dependencies.
```

**解决方案：**
```bash
# 方案1: 使用最小化依赖
pip install -r requirements-minimal.txt

# 方案2: 逐个安装核心依赖
pip install fastapi uvicorn sqlalchemy==1.4.53 aiosqlite paramiko

# 方案3: 使用虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements-minimal.txt
```

### 问题2: 加密库安装失败
```
ERROR: Failed building wheel for cryptography
```

**解决方案：**
```bash
# Windows
pip install --upgrade pip setuptools wheel
pip install cryptography

# Linux/Mac
sudo apt-get install build-essential libffi-dev libssl-dev  # Ubuntu/Debian
# 或
brew install libffi openssl  # macOS

# 如果仍然失败，跳过加密功能
pip install fastapi uvicorn sqlalchemy aiosqlite paramiko
```

## 🚀 快速启动步骤

### 步骤1: 测试基础功能
```bash
# 1. 下载项目文件
# 2. 测试基础FastAPI功能
python test_app.py
```

### 步骤2: 安装最小依赖
```bash
pip install -r requirements-minimal.txt
```

### 步骤3: 启动测试版
```bash
python test_app.py
# 访问 http://localhost:8000
```

### 步骤4: 安装完整功能（可选）
```bash
# 如果步骤3成功，继续安装完整功能
pip install -r requirements.txt
python init_db.py
python start.py
```

## 🐛 常见问题

### 问题1: 模块导入错误
```python
ModuleNotFoundError: No module named 'app'
```

**解决方案：**
```bash
# 确保在项目根目录运行
cd /path/to/project
python test_app.py
```

### 问题2: 数据库连接错误
```
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file
```

**解决方案：**
```bash
# 确保有写入权限
chmod 755 .
mkdir -p uploads logs
python init_db.py
```

### 问题3: 端口被占用
```
OSError: [Errno 48] Address already in use
```

**解决方案：**
```bash
# 查找占用端口的进程
lsof -i :8000  # Linux/Mac
netstat -ano | findstr :8000  # Windows

# 杀死进程或使用其他端口
python test_app.py --port 8001
```

## 🔍 调试模式

### 启用详细日志
```python
# 在 test_app.py 中添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 检查依赖版本
```bash
pip list | grep -E "(fastapi|uvicorn|sqlalchemy|paramiko)"
```

### 测试单个组件
```python
# 测试FastAPI
python -c "import fastapi; print('FastAPI OK')"

# 测试SQLAlchemy
python -c "import sqlalchemy; print('SQLAlchemy OK')"

# 测试SSH
python -c "import paramiko; print('Paramiko OK')"
```

## 🏥 健康检查

访问以下URL检查服务状态：
- http://localhost:8000/health - 基础健康检查
- http://localhost:8000/docs - API文档
- http://localhost:8000/api/test - API测试

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **检查Python版本**: 需要Python 3.8+
2. **更新pip**: `pip install --upgrade pip`
3. **清理缓存**: `pip cache purge`
4. **重新安装**: 删除虚拟环境重新创建

## 🎯 最小可运行版本

如果所有依赖都有问题，可以使用这个最小版本：

```python
# minimal_app.py
from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/")
def read_root():
    return {"message": "Linux Management Console 2.0 - 最小版本"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

```bash
pip install fastapi uvicorn
python minimal_app.py
```

这个版本只需要最基础的依赖，可以验证Python和FastAPI环境是否正常。
