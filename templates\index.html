<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux Management Console 2.0</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #06d6a0;
            --warning-color: #ffd60a;
            --danger-color: #f72585;
            --dark-color: #1d3557;
            --light-color: #f8f9fa;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            min-height: calc(100vh - 40px);
        }
        
        .header {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 30px;
            text-align: center;
        }
        
        .nav-pills .nav-link {
            border-radius: 15px;
            margin: 0 5px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }
        
        .host-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .host-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .host-card.selected {
            border: 2px solid var(--primary-color);
            background: rgba(67, 97, 238, 0.05);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online {
            background: var(--success-color);
            box-shadow: 0 0 10px rgba(6, 214, 160, 0.5);
        }
        
        .status-offline {
            background: var(--danger-color);
            box-shadow: 0 0 10px rgba(247, 37, 133, 0.5);
        }
        
        .terminal-output {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.5s ease;
        }
        
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .result-item {
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 10px 10px 0;
            background: white;
        }
        
        .result-success {
            border-left-color: var(--success-color);
            background: rgba(6, 214, 160, 0.05);
        }
        
        .result-error {
            border-left-color: var(--danger-color);
            background: rgba(247, 37, 133, 0.05);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-terminal me-2"></i>Linux Management Console 2.0</h1>
                <p class="mb-0">基于FastAPI的现代化Linux服务器管理平台</p>
            </div>
            
            <!-- 导航 -->
            <div class="p-4">
                <!-- 用户信息和登录状态 -->
                <div class="d-flex justify-content-between align-items-center mb-3" v-if="currentUser">
                    <div></div>
                    <div class="user-info">
                        <span class="me-2">欢迎, {{ currentUser.username }}</span>
                        <span class="badge bg-secondary me-2">{{ currentUser.role }}</span>
                        <button class="btn btn-sm btn-outline-danger" @click="logout">
                            <i class="bi bi-box-arrow-right me-1"></i>退出
                        </button>
                    </div>
                </div>

                <!-- 登录表单 -->
                <div v-if="!currentUser" class="row justify-content-center">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header text-center">
                                <h5><i class="bi bi-person-circle me-1"></i>用户登录</h5>
                            </div>
                            <div class="card-body">
                                <form @submit.prevent="login">
                                    <div class="mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" v-model="loginForm.username" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">密码</label>
                                        <input type="password" class="form-control" v-model="loginForm.password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100" :disabled="loading">
                                        <span v-if="loading" class="loading-spinner me-2"></span>
                                        <i class="bi bi-box-arrow-in-right me-1" v-else></i>登录
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主导航 -->
                <ul class="nav nav-pills justify-content-center mb-4" v-if="currentUser">
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'dashboard' }"
                           @click="activeTab = 'dashboard'" href="#">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'hosts' }"
                           @click="activeTab = 'hosts'" href="#">
                            <i class="bi bi-server me-1"></i>主机管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'groups' }"
                           @click="activeTab = 'groups'" href="#">
                            <i class="bi bi-collection me-1"></i>主机分组
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'operations' }"
                           @click="activeTab = 'operations'" href="#">
                            <i class="bi bi-terminal me-1"></i>操作执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'scripts' }"
                           @click="activeTab = 'scripts'" href="#">
                            <i class="bi bi-file-code me-1"></i>脚本模板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeTab === 'presets' }"
                           @click="activeTab = 'presets'" href="#">
                            <i class="bi bi-gear me-1"></i>预设管理
                        </a>
                    </li>
                    <li class="nav-item" v-if="isAdmin">
                        <a class="nav-link" :class="{ active: activeTab === 'admin' }"
                           @click="activeTab = 'admin'" href="#">
                            <i class="bi bi-shield-check me-1"></i>系统管理
                        </a>
                    </li>
                </ul>
                
                <!-- 仪表板 -->
                <div v-show="activeTab === 'dashboard'" class="tab-content">
                    <div class="row mb-4">
                        <!-- 统计卡片 -->
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-server" style="font-size: 2rem; color: #007bff;"></i>
                                    <h3 class="mt-2">{{ statistics.total_hosts || 0 }}</h3>
                                    <p class="text-muted">总主机数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-check-circle" style="font-size: 2rem; color: #28a745;"></i>
                                    <h3 class="mt-2">{{ statistics.online_hosts || 0 }}</h3>
                                    <p class="text-muted">在线主机</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-x-circle" style="font-size: 2rem; color: #dc3545;"></i>
                                    <h3 class="mt-2">{{ statistics.offline_hosts || 0 }}</h3>
                                    <p class="text-muted">离线主机</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-clock-history" style="font-size: 2rem; color: #ffc107;"></i>
                                    <h3 class="mt-2">{{ recentTasks.length || 0 }}</h3>
                                    <p class="text-muted">最近任务</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 最近任务 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <i class="bi bi-clock-history me-1"></i>最近任务
                                </div>
                                <div class="card-body">
                                    <div v-if="recentTasks.length === 0" class="text-center text-muted py-3">
                                        暂无最近任务
                                    </div>
                                    <div v-else>
                                        <div v-for="task in recentTasks.slice(0, 5)" :key="task.id" class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>{{ task.task_type }}</strong>
                                                <small class="text-muted d-block">{{ task.host_name }}</small>
                                            </div>
                                            <span class="badge" :class="getTaskStatusClass(task.status)">
                                                {{ task.status }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主机分组统计 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <i class="bi bi-pie-chart me-1"></i>主机分组统计
                                </div>
                                <div class="card-body">
                                    <div v-if="Object.keys(statistics.group_statistics || {}).length === 0" class="text-center text-muted py-3">
                                        暂无分组统计
                                    </div>
                                    <div v-else>
                                        <div v-for="(count, groupName) in statistics.group_statistics" :key="groupName" class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ groupName }}</span>
                                            <span class="badge bg-primary">{{ count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主机管理 -->
                <div v-show="activeTab === 'hosts'" class="tab-content">
                    <div class="card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-server me-1"></i>主机列表</span>
                            <div>
                                <button class="btn btn-sm btn-light me-2" @click="loadHosts">
                                    <i class="bi bi-arrow-repeat me-1"></i>刷新
                                </button>
                                <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#hostModal">
                                    <i class="bi bi-plus-circle me-1"></i>添加主机
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" v-if="hosts.length > 0">
                                <div class="col-md-4 mb-3" v-for="host in hosts" :key="host.id">
                                    <div class="card host-card" 
                                         :class="{ selected: selectedHosts.includes(host.id) }"
                                         @click="toggleHostSelection(host.id)">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ host.name }}</h5>
                                            <p class="card-text">
                                                <i class="bi bi-hdd-network me-1"></i>{{ host.ip }}:{{ host.port }}<br>
                                                <i class="bi bi-person me-1"></i>{{ host.username }}<br>
                                                <span class="status-indicator" 
                                                      :class="host.is_active ? 'status-online' : 'status-offline'"></span>
                                                {{ host.is_active ? '在线' : '离线' }}
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        @click.stop="testConnection(host)">
                                                    <i class="bi bi-wifi me-1"></i>测试连接
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        @click.stop="deleteHost(host.id)">
                                                    <i class="bi bi-trash me-1"></i>删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center py-5">
                                <i class="bi bi-server" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="text-muted mt-3">暂无主机，请添加主机</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主机分组管理 -->
                <div v-show="activeTab === 'groups'" class="tab-content">
                    <div class="card">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-collection me-1"></i>主机分组</span>
                            <div>
                                <button class="btn btn-sm btn-light me-2" @click="loadHostGroups">
                                    <i class="bi bi-arrow-repeat me-1"></i>刷新
                                </button>
                                <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#groupModal">
                                    <i class="bi bi-plus-circle me-1"></i>添加分组
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" v-if="hostGroups.length > 0">
                                <div class="col-md-4 mb-3" v-for="group in hostGroups" :key="group.id">
                                    <div class="card" :style="{ borderLeft: `4px solid ${group.color}` }">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ group.name }}</h5>
                                            <p class="card-text">
                                                {{ group.description || '无描述' }}<br>
                                                <small class="text-muted">主机数量: {{ getGroupHostCount(group.id) }}</small>
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" @click="viewGroupHosts(group.id)">
                                                    <i class="bi bi-eye me-1"></i>查看主机
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" @click="editGroup(group)">
                                                    <i class="bi bi-pencil me-1"></i>编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" @click="deleteGroup(group.id)">
                                                    <i class="bi bi-trash me-1"></i>删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center py-5">
                                <i class="bi bi-collection" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="text-muted mt-3">暂无主机分组，请添加分组</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 脚本模板管理 -->
                <div v-show="activeTab === 'scripts'" class="tab-content">
                    <div class="card">
                        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-file-code me-1"></i>脚本模板</span>
                            <div>
                                <button class="btn btn-sm btn-dark me-2" @click="loadScriptTemplates">
                                    <i class="bi bi-arrow-repeat me-1"></i>刷新
                                </button>
                                <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#scriptModal">
                                    <i class="bi bi-plus-circle me-1"></i>添加模板
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 分类筛选 -->
                            <div class="mb-3">
                                <label class="form-label">按分类筛选:</label>
                                <select class="form-select" v-model="selectedCategory" @change="loadScriptTemplates">
                                    <option value="">所有分类</option>
                                    <option v-for="category in scriptCategories" :key="category" :value="category">
                                        {{ category }}
                                    </option>
                                </select>
                            </div>

                            <div class="row" v-if="scriptTemplates.length > 0">
                                <div class="col-md-6 mb-3" v-for="template in scriptTemplates" :key="template.id">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ template.name }}</h5>
                                            <p class="card-text">
                                                {{ template.description || '无描述' }}<br>
                                                <span class="badge bg-secondary me-1">{{ template.category || '未分类' }}</span>
                                                <span class="badge bg-info">v{{ template.version }}</span>
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-primary" @click="executeScript(template)">
                                                    <i class="bi bi-play-circle me-1"></i>执行
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" @click="viewScript(template)">
                                                    <i class="bi bi-eye me-1"></i>查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" @click="editScript(template)">
                                                    <i class="bi bi-pencil me-1"></i>编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" @click="cloneScript(template)">
                                                    <i class="bi bi-files me-1"></i>克隆
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center py-5">
                                <i class="bi bi-file-code" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="text-muted mt-3">暂无脚本模板</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作执行 -->
                <div v-show="activeTab === 'operations'" class="tab-content">
                    <div class="row">
                        <!-- 文件上传分发 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <i class="bi bi-file-earmark-arrow-up me-1"></i>文件分发
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">选择文件</label>
                                        <input type="file" class="form-control" ref="fileInput" @change="handleFileSelect">
                                    </div>
                                    <div class="mb-3" v-if="uploadedFile">
                                        <div class="alert alert-success">
                                            <i class="bi bi-check-circle me-1"></i>已上传: {{ uploadedFile.original_filename }}
                                        </div>
                                        <label class="form-label">远程路径</label>
                                        <input type="text" class="form-control" v-model="remotePath" placeholder="/tmp/">
                                        <button class="btn btn-success mt-2" @click="distributeFile" :disabled="loading">
                                            <span v-if="loading" class="loading-spinner me-2"></span>
                                            <i class="bi bi-send me-1" v-else></i>分发到选中主机
                                        </button>
                                    </div>
                                    <button v-else class="btn btn-primary" @click="uploadFile" :disabled="loading">
                                        <span v-if="loading" class="loading-spinner me-2"></span>
                                        <i class="bi bi-cloud-upload me-1" v-else></i>上传文件
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 命令执行 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <i class="bi bi-terminal me-1"></i>命令执行
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">命令</label>
                                        <textarea class="form-control" v-model="command" rows="3" 
                                                  placeholder="例如: ls -la /home"></textarea>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" v-model="useSudo" id="sudoCheck">
                                        <label class="form-check-label" for="sudoCheck">使用sudo执行</label>
                                    </div>
                                    <button class="btn btn-warning" @click="executeCommand" :disabled="loading">
                                        <span v-if="loading" class="loading-spinner me-2"></span>
                                        <i class="bi bi-play-circle me-1" v-else></i>在选中主机上执行
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 预设管理 -->
                <div v-show="activeTab === 'presets'" class="tab-content">
                    <div class="card">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <span><i class="bi bi-gear me-1"></i>预设操作</span>
                            <button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#presetModal">
                                <i class="bi bi-plus-circle me-1"></i>添加预设
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row" v-if="presets.length > 0">
                                <div class="col-md-4 mb-3" v-for="preset in presets" :key="preset.id">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">{{ preset.name }}</h5>
                                            <p class="card-text">
                                                <span class="badge" :class="preset.type === 'file' ? 'bg-info' : 'bg-warning'">
                                                    {{ preset.type === 'file' ? '文件分发' : '命令执行' }}
                                                </span><br>
                                                <small class="text-muted">主机数量: {{ preset.host_ids.length }}</small>
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-primary" @click="executePreset(preset)">
                                                    <i class="bi bi-play-circle me-1"></i>执行
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" @click="deletePreset(preset.id)">
                                                    <i class="bi bi-trash me-1"></i>删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center py-5">
                                <i class="bi bi-gear" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="text-muted mt-3">暂无预设操作</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 结果显示 -->
                <div class="card mt-4" v-if="results.length > 0">
                    <div class="card-header bg-secondary text-white">
                        <i class="bi bi-clipboard-data me-1"></i>执行结果
                    </div>
                    <div class="card-body">
                        <div v-for="result in results" :key="result.host_id" 
                             class="result-item" :class="result.status === 'success' ? 'result-success' : 'result-error'">
                            <h6><i class="bi bi-server me-1"></i>{{ result.host_name }}</h6>
                            <div v-if="result.output" class="terminal-output">{{ result.output }}</div>
                            <div v-if="result.error" class="text-danger mt-2">
                                <strong>错误:</strong> {{ result.error }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加主机模态框 -->
        <div class="modal fade" id="hostModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-server me-1"></i>添加主机</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="saveHost">
                            <div class="mb-3">
                                <label class="form-label">主机名称</label>
                                <input type="text" class="form-control" v-model="newHost.name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">IP地址</label>
                                <input type="text" class="form-control" v-model="newHost.ip" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">端口</label>
                                <input type="number" class="form-control" v-model="newHost.port" value="22">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" v-model="newHost.username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">密码（可选）</label>
                                <input type="password" class="form-control" v-model="newHost.password">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveHost">保存</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加预设模态框 -->
        <div class="modal fade" id="presetModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-gear me-1"></i>添加预设操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="savePreset">
                            <div class="mb-3">
                                <label class="form-label">预设名称</label>
                                <input type="text" class="form-control" v-model="newPreset.name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">操作类型</label>
                                <select class="form-select" v-model="newPreset.type" required>
                                    <option value="file">文件分发</option>
                                    <option value="command">命令执行</option>
                                </select>
                            </div>
                            <div v-if="newPreset.type === 'command'" class="mb-3">
                                <label class="form-label">命令</label>
                                <textarea class="form-control" v-model="newPreset.command" rows="3"></textarea>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" v-model="newPreset.use_sudo" id="presetSudo">
                                    <label class="form-check-label" for="presetSudo">使用sudo执行</label>
                                </div>
                            </div>
                            <div v-if="newPreset.type === 'file'">
                                <div class="mb-3">
                                    <label class="form-label">本地文件</label>
                                    <input type="text" class="form-control" v-model="newPreset.local_file">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">远程路径</label>
                                    <input type="text" class="form-control" v-model="newPreset.remote_path" value="/tmp/">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">选择主机</label>
                                <div class="row">
                                    <div class="col-md-6" v-for="host in hosts" :key="host.id">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   :value="host.id" v-model="newPreset.host_ids" :id="'preset-host-' + host.id">
                                            <label class="form-check-label" :for="'preset-host-' + host.id">
                                                {{ host.name }} ({{ host.ip }})
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="savePreset">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    activeTab: 'dashboard',
                    loading: false,

                    // 用户认证
                    currentUser: null,
                    loginForm: {
                        username: '',
                        password: ''
                    },

                    // 基础数据
                    hosts: [],
                    hostGroups: [],
                    scriptTemplates: [],
                    scriptCategories: [],
                    presets: [],
                    selectedHosts: [],
                    results: [],
                    statistics: {},
                    recentTasks: [],

                    // 操作相关
                    uploadedFile: null,
                    remotePath: '/tmp/',
                    command: '',
                    useSudo: false,
                    selectedCategory: '',

                    // WebSocket
                    websocket: null,
                    // 表单数据
                    newHost: {
                        name: '',
                        ip: '',
                        port: 22,
                        username: '',
                        password: '',
                        description: '',
                        tags: [],
                        group_id: null
                    },
                    newGroup: {
                        name: '',
                        description: '',
                        color: '#007bff'
                    },
                    newScript: {
                        name: '',
                        description: '',
                        content: '',
                        category: '',
                        parameters: []
                    },
                    newPreset: {
                        name: '',
                        type: 'command',
                        command: '',
                        use_sudo: false,
                        local_file: '',
                        remote_path: '/tmp/',
                        host_ids: []
                    }
                }
            },
            computed: {
                isAdmin() {
                    return this.currentUser && ['super_admin', 'admin'].includes(this.currentUser.role);
                },
                authHeaders() {
                    const token = localStorage.getItem('access_token');
                    return token ? { 'Authorization': `Bearer ${token}` } : {};
                }
            },
            mounted() {
                this.checkAuth();
                this.initWebSocket();
            },
            methods: {
                // 认证相关方法
                checkAuth() {
                    const token = localStorage.getItem('access_token');
                    const user = localStorage.getItem('current_user');
                    if (token && user) {
                        this.currentUser = JSON.parse(user);
                        this.loadDashboardData();
                    }
                },

                async login() {
                    this.loading = true;
                    try {
                        const response = await axios.post('/api/auth/login', this.loginForm, {
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            transformRequest: [function (data) {
                                return Object.keys(data).map(key => `${key}=${encodeURIComponent(data[key])}`).join('&');
                            }]
                        });

                        const { access_token, user } = response.data;
                        localStorage.setItem('access_token', access_token);
                        localStorage.setItem('current_user', JSON.stringify(user));
                        this.currentUser = user;
                        this.loginForm = { username: '', password: '' };
                        this.loadDashboardData();
                        this.showSuccess('登录成功');
                    } catch (error) {
                        this.showError('登录失败，请检查用户名和密码');
                    } finally {
                        this.loading = false;
                    }
                },

                logout() {
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('current_user');
                    this.currentUser = null;
                    this.activeTab = 'dashboard';
                    if (this.websocket) {
                        this.websocket.close();
                        this.websocket = null;
                    }
                },

                // WebSocket相关方法
                initWebSocket() {
                    const token = localStorage.getItem('access_token');
                    if (!token) return;

                    const wsUrl = `ws://localhost:8000/ws?token=${token}`;
                    this.websocket = new WebSocket(wsUrl);

                    this.websocket.onopen = () => {
                        console.log('WebSocket连接已建立');
                    };

                    this.websocket.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        this.handleWebSocketMessage(message);
                    };

                    this.websocket.onclose = () => {
                        console.log('WebSocket连接已关闭');
                        // 重连逻辑
                        setTimeout(() => {
                            if (this.currentUser) {
                                this.initWebSocket();
                            }
                        }, 5000);
                    };
                },

                handleWebSocketMessage(message) {
                    switch (message.type) {
                        case 'task_update':
                            this.handleTaskUpdate(message.data);
                            break;
                        case 'host_status_update':
                            this.handleHostStatusUpdate(message.data);
                            break;
                        case 'system_alert':
                            this.showAlert(message.data);
                            break;
                    }
                },

                // 数据加载方法
                async loadDashboardData() {
                    if (!this.currentUser) return;

                    await Promise.all([
                        this.loadHosts(),
                        this.loadHostGroups(),
                        this.loadScriptTemplates(),
                        this.loadPresets(),
                        this.loadStatistics(),
                        this.loadRecentTasks()
                    ]);
                },

                async loadHosts() {
                    try {
                        const response = await axios.get('/api/hosts/', { headers: this.authHeaders });
                        this.hosts = response.data;
                    } catch (error) {
                        this.showError('加载主机列表失败');
                    }
                },

                async loadHostGroups() {
                    try {
                        const response = await axios.get('/api/host-groups/', { headers: this.authHeaders });
                        this.hostGroups = response.data;
                    } catch (error) {
                        this.showError('加载主机分组失败');
                    }
                },

                async loadScriptTemplates() {
                    try {
                        const params = this.selectedCategory ? { category: this.selectedCategory } : {};
                        const response = await axios.get('/api/script-templates/', {
                            headers: this.authHeaders,
                            params
                        });
                        this.scriptTemplates = response.data;

                        // 加载分类
                        const categoriesResponse = await axios.get('/api/script-templates/categories', { headers: this.authHeaders });
                        this.scriptCategories = categoriesResponse.data;
                    } catch (error) {
                        this.showError('加载脚本模板失败');
                    }
                },

                async loadStatistics() {
                    try {
                        const response = await axios.get('/api/hosts/statistics', { headers: this.authHeaders });
                        this.statistics = response.data;
                    } catch (error) {
                        this.showError('加载统计信息失败');
                    }
                },

                async loadRecentTasks() {
                    try {
                        // 这里需要实现获取最近任务的API
                        // const response = await axios.get('/api/tasks/recent', { headers: this.authHeaders });
                        // this.recentTasks = response.data;
                    } catch (error) {
                        console.error('加载最近任务失败');
                    }
                },

                async loadPresets() {
                    try {
                        const response = await axios.get('/api/presets/', { headers: this.authHeaders });
                        this.presets = response.data;
                    } catch (error) {
                        this.showError('加载预设列表失败');
                    }
                },

                // 工具方法
                getGroupHostCount(groupId) {
                    return this.hosts.filter(host => host.group_id === groupId).length;
                },

                getTaskStatusClass(status) {
                    const statusClasses = {
                        'pending': 'bg-warning',
                        'running': 'bg-info',
                        'success': 'bg-success',
                        'failed': 'bg-danger'
                    };
                    return statusClasses[status] || 'bg-secondary';
                },

                handleTaskUpdate(data) {
                    // 处理任务状态更新
                    console.log('任务更新:', data);
                    this.loadRecentTasks();
                },

                handleHostStatusUpdate(data) {
                    // 处理主机状态更新
                    const host = this.hosts.find(h => h.id === data.host_id);
                    if (host) {
                        host.last_connected = data.last_connected;
                        // 更新统计信息
                        this.loadStatistics();
                    }
                },

                showAlert(data) {
                    // 显示系统告警
                    const alertClass = {
                        'info': 'alert-info',
                        'warning': 'alert-warning',
                        'error': 'alert-danger',
                        'success': 'alert-success'
                    }[data.level] || 'alert-info';

                    // 这里可以使用更好的通知组件
                    alert(`${data.title}: ${data.message}`);
                },

                // 主机分组相关方法
                async saveGroup() {
                    try {
                        await axios.post('/api/host-groups/', this.newGroup, { headers: this.authHeaders });
                        this.showSuccess('主机分组创建成功');
                        this.loadHostGroups();
                        this.resetNewGroup();
                        bootstrap.Modal.getInstance(document.getElementById('groupModal')).hide();
                    } catch (error) {
                        this.showError('创建主机分组失败');
                    }
                },

                async deleteGroup(groupId) {
                    if (confirm('确定要删除此主机分组吗？分组下的主机将移到未分组。')) {
                        try {
                            await axios.delete(`/api/host-groups/${groupId}`, { headers: this.authHeaders });
                            this.showSuccess('主机分组删除成功');
                            this.loadHostGroups();
                            this.loadHosts();
                        } catch (error) {
                            this.showError('删除主机分组失败');
                        }
                    }
                },

                viewGroupHosts(groupId) {
                    this.activeTab = 'hosts';
                    // 这里可以添加按分组筛选的逻辑
                },

                editGroup(group) {
                    this.newGroup = { ...group };
                    // 打开编辑模态框
                },

                // 脚本模板相关方法
                async saveScript() {
                    try {
                        await axios.post('/api/script-templates/', this.newScript, { headers: this.authHeaders });
                        this.showSuccess('脚本模板创建成功');
                        this.loadScriptTemplates();
                        this.resetNewScript();
                        bootstrap.Modal.getInstance(document.getElementById('scriptModal')).hide();
                    } catch (error) {
                        this.showError('创建脚本模板失败');
                    }
                },

                async executeScript(template) {
                    // 执行脚本模板
                    if (this.selectedHosts.length === 0) {
                        this.showError('请先选择要执行的主机');
                        return;
                    }

                    try {
                        const parameters = {};
                        // 这里可以添加参数输入界面

                        const response = await axios.post(`/api/script-templates/${template.id}/execute`, {
                            parameters,
                            host_ids: this.selectedHosts
                        }, { headers: this.authHeaders });

                        this.results = response.data.results;
                        this.showSuccess('脚本执行完成');
                    } catch (error) {
                        this.showError('脚本执行失败');
                    }
                },

                viewScript(template) {
                    // 查看脚本内容
                    alert(`脚本内容:\n${template.content}`);
                },

                editScript(template) {
                    this.newScript = { ...template };
                    // 打开编辑模态框
                },

                async cloneScript(template) {
                    const newName = prompt('请输入新脚本名称:', `${template.name} - 副本`);
                    if (newName) {
                        try {
                            await axios.post(`/api/script-templates/${template.id}/clone`, {
                                new_name: newName
                            }, { headers: this.authHeaders });
                            this.showSuccess('脚本模板克隆成功');
                            this.loadScriptTemplates();
                        } catch (error) {
                            this.showError('克隆脚本模板失败');
                        }
                    }
                },
                
                toggleHostSelection(hostId) {
                    const index = this.selectedHosts.indexOf(hostId);
                    if (index > -1) {
                        this.selectedHosts.splice(index, 1);
                    } else {
                        this.selectedHosts.push(hostId);
                    }
                },
                
                async testConnection(host) {
                    try {
                        const response = await axios.post(`/api/hosts/${host.id}/test`);
                        if (response.data.success) {
                            this.showSuccess(`${host.name} 连接测试成功`);
                        } else {
                            this.showError(`${host.name} 连接测试失败: ${response.data.error}`);
                        }
                    } catch (error) {
                        this.showError(`连接测试失败: ${error.message}`);
                    }
                },
                
                async saveHost() {
                    try {
                        await axios.post('/api/hosts/', this.newHost);
                        this.showSuccess('主机添加成功');
                        this.loadHosts();
                        this.resetNewHost();
                        bootstrap.Modal.getInstance(document.getElementById('hostModal')).hide();
                    } catch (error) {
                        this.showError('添加主机失败');
                    }
                },
                
                async deleteHost(hostId) {
                    if (confirm('确定要删除此主机吗？')) {
                        try {
                            await axios.delete(`/api/hosts/${hostId}`);
                            this.showSuccess('主机删除成功');
                            this.loadHosts();
                        } catch (error) {
                            this.showError('删除主机失败');
                        }
                    }
                },
                
                async uploadFile() {
                    const fileInput = this.$refs.fileInput;
                    if (!fileInput.files[0]) {
                        this.showError('请选择文件');
                        return;
                    }
                    
                    const formData = new FormData();
                    formData.append('file', fileInput.files[0]);
                    
                    this.loading = true;
                    try {
                        const response = await axios.post('/api/operations/upload', formData);
                        this.uploadedFile = response.data;
                        this.showSuccess('文件上传成功');
                    } catch (error) {
                        this.showError('文件上传失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async distributeFile() {
                    if (this.selectedHosts.length === 0) {
                        this.showError('请选择至少一个主机');
                        return;
                    }
                    
                    this.loading = true;
                    try {
                        const response = await axios.post('/api/operations/distribute', {
                            filename: this.uploadedFile.filename,
                            remote_path: this.remotePath,
                            host_ids: this.selectedHosts
                        });
                        this.results = response.data.results;
                        this.showSuccess('文件分发完成');
                    } catch (error) {
                        this.showError('文件分发失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async executeCommand() {
                    if (!this.command.trim()) {
                        this.showError('请输入命令');
                        return;
                    }
                    
                    if (this.selectedHosts.length === 0) {
                        this.showError('请选择至少一个主机');
                        return;
                    }
                    
                    this.loading = true;
                    try {
                        const response = await axios.post('/api/operations/execute', {
                            command: this.command,
                            host_ids: this.selectedHosts,
                            use_sudo: this.useSudo
                        });
                        this.results = response.data.results;
                        this.showSuccess('命令执行完成');
                    } catch (error) {
                        this.showError('命令执行失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async savePreset() {
                    try {
                        await axios.post('/api/presets/', this.newPreset);
                        this.showSuccess('预设添加成功');
                        this.loadPresets();
                        this.resetNewPreset();
                        bootstrap.Modal.getInstance(document.getElementById('presetModal')).hide();
                    } catch (error) {
                        this.showError('添加预设失败');
                    }
                },
                
                async executePreset(preset) {
                    this.loading = true;
                    try {
                        let response;
                        if (preset.type === 'command') {
                            response = await axios.post('/api/operations/execute', {
                                command: preset.command,
                                host_ids: preset.host_ids,
                                use_sudo: preset.use_sudo
                            });
                        } else {
                            response = await axios.post('/api/operations/distribute', {
                                filename: preset.local_file,
                                remote_path: preset.remote_path,
                                host_ids: preset.host_ids
                            });
                        }
                        this.results = response.data.results;
                        this.showSuccess(`预设操作 "${preset.name}" 执行完成`);
                    } catch (error) {
                        this.showError('预设操作执行失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async deletePreset(presetId) {
                    if (confirm('确定要删除此预设操作吗？')) {
                        try {
                            await axios.delete(`/api/presets/${presetId}`);
                            this.showSuccess('预设删除成功');
                            this.loadPresets();
                        } catch (error) {
                            this.showError('删除预设失败');
                        }
                    }
                },
                
                handleFileSelect(event) {
                    this.uploadedFile = null;
                },
                
                resetNewHost() {
                    this.newHost = {
                        name: '',
                        ip: '',
                        port: 22,
                        username: '',
                        password: '',
                        description: '',
                        tags: [],
                        group_id: null
                    };
                },

                resetNewGroup() {
                    this.newGroup = {
                        name: '',
                        description: '',
                        color: '#007bff'
                    };
                },

                resetNewScript() {
                    this.newScript = {
                        name: '',
                        description: '',
                        content: '',
                        category: '',
                        parameters: []
                    };
                },

                resetNewPreset() {
                    this.newPreset = {
                        name: '',
                        type: 'command',
                        command: '',
                        use_sudo: false,
                        local_file: '',
                        remote_path: '/tmp/',
                        host_ids: []
                    };
                },
                
                showSuccess(message) {
                    // 简单的成功提示，可以替换为更好的通知组件
                    alert('成功: ' + message);
                },
                
                showError(message) {
                    // 简单的错误提示，可以替换为更好的通知组件
                    alert('错误: ' + message);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
