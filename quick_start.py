#!/usr/bin/env python3
"""
快速启动脚本 - 最小化依赖版本
"""
import os
import sys
import subprocess
from pathlib import Path

# 最小化依赖列表
MINIMAL_REQUIREMENTS = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "sqlalchemy==1.4.53",
    "aiosqlite==0.19.0",
    "paramiko==3.3.1",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.6",
    "pydantic==2.5.0",
    "python-dotenv==1.0.0",
    "aiofiles==23.2.1"
]

def install_minimal_deps():
    """安装最小化依赖"""
    print("📦 安装最小化依赖...")
    for package in MINIMAL_REQUIREMENTS:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✅ {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} - {e}")
            return False
    return True

def create_minimal_env():
    """创建最小化环境配置"""
    env_content = """# 最小化配置
DATABASE_URL=sqlite+aiosqlite:///./app.db
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=True
APP_NAME=Linux Management Console
UPLOAD_DIR=./uploads
LOG_FILE=./logs/app.log
SSH_TIMEOUT=30
"""
    
    with open(".env", "w") as f:
        f.write(env_content)
    print("✅ 环境配置文件创建完成")

def create_minimal_main():
    """创建简化的主应用文件"""
    main_content = '''"""
简化版主应用
"""
import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse

# 确保目录存在
os.makedirs("uploads", exist_ok=True)
os.makedirs("logs", exist_ok=True)
os.makedirs("static", exist_ok=True)

app = FastAPI(title="Linux Management Console - 简化版")

# 静态文件
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Linux Management Console</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h1>🐧 Linux Management Console 2.0</h1>
                            <p class="mb-0">简化版 - 基础功能测试</p>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h4>✅ FastAPI 应用启动成功！</h4>
                                <p>这是一个简化版本，用于测试基础功能。</p>
                            </div>
                            
                            <h5>📚 可用的API端点：</h5>
                            <ul>
                                <li><a href="/docs" target="_blank">📖 API文档 (Swagger UI)</a></li>
                                <li><a href="/redoc" target="_blank">📋 API文档 (ReDoc)</a></li>
                                <li><a href="/health">🏥 健康检查</a></li>
                            </ul>
                            
                            <h5>🚀 下一步：</h5>
                            <ol>
                                <li>确认基础功能正常</li>
                                <li>安装完整依赖: <code>pip install -r requirements.txt</code></li>
                                <li>初始化数据库: <code>python init_db.py</code></li>
                                <li>启动完整版本: <code>python main.py</code></li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "简化版运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open("simple_main.py", "w", encoding="utf-8") as f:
        f.write(main_content)
    print("✅ 简化主应用文件创建完成")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Linux Management Console 2.0 - 快速启动")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装最小化依赖
    if not install_minimal_deps():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 创建配置文件
    if not Path(".env").exists():
        create_minimal_env()
    
    # 创建简化主应用
    create_minimal_main()
    
    print("\n" + "=" * 60)
    print("🎉 快速启动准备完成！")
    print("\n启动方式：")
    print("1. 简化版测试: python simple_main.py")
    print("2. 完整版启动: python start.py")
    print("\n访问地址: http://localhost:8000")
    print("=" * 60)
    
    # 询问是否立即启动
    start_now = input("\n是否立即启动简化版进行测试？(y/N): ").lower().strip()
    if start_now in ['y', 'yes']:
        print("\n🚀 启动简化版...")
        try:
            subprocess.run([sys.executable, "simple_main.py"], check=True)
        except KeyboardInterrupt:
            print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
