#!/usr/bin/env python3
"""
测试应用 - 验证基础功能
"""
import os
import sys
from pathlib import Path

# 确保目录存在
os.makedirs("uploads", exist_ok=True)
os.makedirs("logs", exist_ok=True)
os.makedirs("static", exist_ok=True)

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
    
    print("✅ FastAPI 导入成功")
except ImportError as e:
    print(f"❌ FastAPI 导入失败: {e}")
    print("请先安装依赖: pip install fastapi uvicorn")
    sys.exit(1)

app = FastAPI(
    title="Linux Management Console 2.0 - 测试版",
    description="用于测试基础功能的简化版本",
    version="2.0.0-test"
)

# 静态文件
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except Exception:
    pass

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Linux Management Console 2.0 - 测试版</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white text-center">
                            <h1><i class="bi bi-terminal me-2"></i>Linux Management Console 2.0</h1>
                            <p class="mb-0">测试版 - 验证基础功能</p>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h4><i class="bi bi-check-circle me-2"></i>FastAPI 应用启动成功！</h4>
                                <p class="mb-0">基础框架运行正常，可以继续安装完整功能。</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="bi bi-book me-1"></i>API文档</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <a href="/docs" target="_blank" class="text-decoration-none">
                                                <i class="bi bi-file-text me-1"></i>Swagger UI
                                            </a>
                                        </li>
                                        <li class="list-group-item">
                                            <a href="/redoc" target="_blank" class="text-decoration-none">
                                                <i class="bi bi-file-earmark-text me-1"></i>ReDoc
                                            </a>
                                        </li>
                                        <li class="list-group-item">
                                            <a href="/health" target="_blank" class="text-decoration-none">
                                                <i class="bi bi-heart-pulse me-1"></i>健康检查
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5><i class="bi bi-gear me-1"></i>下一步操作</h5>
                                    <ol class="list-group list-group-numbered">
                                        <li class="list-group-item">
                                            <strong>安装完整依赖</strong><br>
                                            <code>pip install -r requirements.txt</code>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>初始化数据库</strong><br>
                                            <code>python init_db.py</code>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>启动完整版本</strong><br>
                                            <code>python start.py</code>
                                        </li>
                                    </ol>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h5><i class="bi bi-info-circle me-1"></i>项目信息</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <i class="bi bi-speedometer2" style="font-size: 2rem; color: #0d6efd;"></i>
                                                <h6 class="mt-2">高性能</h6>
                                                <small>FastAPI异步框架</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <i class="bi bi-shield-check" style="font-size: 2rem; color: #198754;"></i>
                                                <h6 class="mt-2">安全可靠</h6>
                                                <small>JWT认证 + 权限控制</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <i class="bi bi-puzzle" style="font-size: 2rem; color: #fd7e14;"></i>
                                                <h6 class="mt-2">易扩展</h6>
                                                <small>模块化设计</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-center text-muted">
                            <small>Linux Management Console 2.0 - 基于 FastAPI + Vue.js</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "测试版运行正常",
        "version": "2.0.0-test",
        "framework": "FastAPI"
    }

@app.get("/api/test")
async def test_api():
    """测试API"""
    return {
        "message": "API测试成功",
        "features": [
            "FastAPI框架",
            "异步处理",
            "自动API文档",
            "类型验证"
        ]
    }

def main():
    """主函数"""
    print("🚀 启动 Linux Management Console 2.0 测试版...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
