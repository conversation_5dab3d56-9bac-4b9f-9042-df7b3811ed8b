"""
脚本模板管理API路由
"""
import re
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.models import (
    ScriptTemplateCreate, ScriptTemplateUpdate, ScriptTemplateResponse, User
)
from app.crud import ScriptTemplateCRUD, AuditLogCRUD
from app.auth import get_current_active_user, require_permissions

router = APIRouter(prefix="/api/script-templates", tags=["script-templates"])


class ScriptTemplateService:
    """脚本模板服务"""
    
    @staticmethod
    def validate_script_content(content: str) -> bool:
        """验证脚本内容"""
        # 基本的脚本安全检查
        dangerous_patterns = [
            r'rm\s+-rf\s+/',  # 危险的删除命令
            r':\(\)\{.*\}',   # Fork炸弹
            r'mkfs\.',        # 格式化命令
            r'dd\s+if=.*of=/dev/',  # 危险的dd命令
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return False
        
        return True
    
    @staticmethod
    def extract_parameters(content: str) -> List[Dict[str, Any]]:
        """从脚本内容中提取参数"""
        # 查找 {{parameter_name}} 格式的参数
        pattern = r'\{\{(\w+)\}\}'
        matches = re.findall(pattern, content)
        
        parameters = []
        for param in set(matches):  # 去重
            parameters.append({
                "name": param,
                "type": "string",
                "required": True,
                "description": f"参数 {param}",
                "default": ""
            })
        
        return parameters
    
    @staticmethod
    def render_script(content: str, parameters: Dict[str, str]) -> str:
        """渲染脚本，替换参数"""
        rendered_content = content
        
        for param_name, param_value in parameters.items():
            placeholder = f"{{{{{param_name}}}}}"
            rendered_content = rendered_content.replace(placeholder, str(param_value))
        
        return rendered_content
    
    @staticmethod
    def validate_parameters(template_params: List[Dict], provided_params: Dict[str, str]) -> List[str]:
        """验证提供的参数"""
        errors = []
        
        for param in template_params:
            param_name = param.get("name")
            param_required = param.get("required", False)
            param_type = param.get("type", "string")
            
            if param_required and param_name not in provided_params:
                errors.append(f"缺少必需参数: {param_name}")
                continue
            
            if param_name in provided_params:
                value = provided_params[param_name]
                
                # 类型验证
                if param_type == "integer":
                    try:
                        int(value)
                    except ValueError:
                        errors.append(f"参数 {param_name} 必须是整数")
                elif param_type == "float":
                    try:
                        float(value)
                    except ValueError:
                        errors.append(f"参数 {param_name} 必须是数字")
                elif param_type == "boolean":
                    if value.lower() not in ["true", "false", "1", "0", "yes", "no"]:
                        errors.append(f"参数 {param_name} 必须是布尔值")
        
        return errors


@router.post("/", response_model=ScriptTemplateResponse, status_code=status.HTTP_201_CREATED)
async def create_script_template(
    template_data: ScriptTemplateCreate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:write"))
):
    """创建脚本模板"""
    # 验证脚本内容安全性
    if not ScriptTemplateService.validate_script_content(template_data.content):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="脚本内容包含危险命令，请检查后重试"
        )
    
    # 自动提取参数（如果未提供）
    if not template_data.parameters:
        template_data.parameters = ScriptTemplateService.extract_parameters(template_data.content)
    
    try:
        template = await ScriptTemplateCRUD.create(db, template_data, current_user.id)
        
        # 记录操作
        await AuditLogCRUD.create(
            db, current_user.id, "script_template_created", "script_template", str(template.id),
            {"name": template.name, "category": template.category}, request.client.host
        )
        
        return template
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建脚本模板失败: {str(e)}"
        )


@router.get("/", response_model=List[ScriptTemplateResponse])
async def get_script_templates(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = Query(None, description="按分类筛选"),
    search: Optional[str] = Query(None, description="搜索模板名称或描述"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取脚本模板列表"""
    templates = await ScriptTemplateCRUD.get_all(
        db, skip=skip, limit=limit, category=category, search=search
    )
    return templates


@router.get("/categories", response_model=List[str])
async def get_template_categories(
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有模板分类"""
    categories = await ScriptTemplateCRUD.get_categories(db)
    return categories


@router.get("/{template_id}", response_model=ScriptTemplateResponse)
async def get_script_template(
    template_id: int,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个脚本模板"""
    template = await ScriptTemplateCRUD.get(db, template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="脚本模板不存在"
        )
    return template


@router.put("/{template_id}", response_model=ScriptTemplateResponse)
async def update_script_template(
    template_id: int,
    template_data: ScriptTemplateUpdate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:write"))
):
    """更新脚本模板"""
    # 验证脚本内容安全性
    if template_data.content and not ScriptTemplateService.validate_script_content(template_data.content):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="脚本内容包含危险命令，请检查后重试"
        )
    
    template = await ScriptTemplateCRUD.update(db, template_id, template_data)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="脚本模板不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "script_template_updated", "script_template", str(template_id),
        template_data.dict(exclude_unset=True), request.client.host
    )
    
    return template


@router.delete("/{template_id}")
async def delete_script_template(
    template_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:write"))
):
    """删除脚本模板"""
    success = await ScriptTemplateCRUD.delete(db, template_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="脚本模板不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "script_template_deleted", "script_template", str(template_id),
        {}, request.client.host
    )
    
    return {"message": "脚本模板删除成功"}


@router.post("/{template_id}/render")
async def render_script_template(
    template_id: int,
    parameters: Dict[str, str],
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:execute"))
):
    """渲染脚本模板"""
    template = await ScriptTemplateCRUD.get(db, template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="脚本模板不存在"
        )
    
    # 验证参数
    template_params = template.parameters or []
    validation_errors = ScriptTemplateService.validate_parameters(template_params, parameters)
    if validation_errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数验证失败: {'; '.join(validation_errors)}"
        )
    
    # 渲染脚本
    rendered_content = ScriptTemplateService.render_script(template.content, parameters)
    
    return {
        "template_id": template_id,
        "template_name": template.name,
        "rendered_content": rendered_content,
        "parameters_used": parameters
    }


@router.post("/{template_id}/execute")
async def execute_script_template(
    template_id: int,
    parameters: Dict[str, str],
    host_ids: List[int],
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:execute"))
):
    """执行脚本模板"""
    template = await ScriptTemplateCRUD.get(db, template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="脚本模板不存在"
        )
    
    # 验证参数
    template_params = template.parameters or []
    validation_errors = ScriptTemplateService.validate_parameters(template_params, parameters)
    if validation_errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数验证失败: {'; '.join(validation_errors)}"
        )
    
    # 渲染脚本
    rendered_content = ScriptTemplateService.render_script(template.content, parameters)
    
    # 执行脚本（调用命令执行API）
    from app.routers.operations import execute_command
    from app.models import CommandExecuteRequest
    
    command_request = CommandExecuteRequest(
        command=rendered_content,
        host_ids=host_ids,
        use_sudo=False  # 可以根据模板配置决定
    )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "script_template_executed", "script_template", str(template_id),
        {
            "template_name": template.name,
            "parameters": parameters,
            "host_ids": host_ids,
            "rendered_content": rendered_content[:500]  # 只记录前500字符
        },
        request.client.host
    )
    
    # 执行命令
    return await execute_command(command_request, db, None)  # 这里需要调整参数


@router.post("/{template_id}/clone")
async def clone_script_template(
    template_id: int,
    new_name: str,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_permissions("script:write"))
):
    """克隆脚本模板"""
    original_template = await ScriptTemplateCRUD.get(db, template_id)
    if not original_template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="原始脚本模板不存在"
        )
    
    # 创建克隆模板
    clone_data = ScriptTemplateCreate(
        name=new_name,
        description=f"克隆自: {original_template.name}",
        content=original_template.content,
        parameters=original_template.parameters,
        category=original_template.category
    )
    
    cloned_template = await ScriptTemplateCRUD.create(db, clone_data, current_user.id)
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "script_template_cloned", "script_template", str(cloned_template.id),
        {
            "original_template_id": template_id,
            "original_name": original_template.name,
            "new_name": new_name
        },
        request.client.host
    )
    
    return cloned_template
