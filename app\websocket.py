"""
WebSocket实时通信模块
"""
import json
import asyncio
import logging
from typing import Dict, List, Set
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.models import WebSocketMessage, User
from app.auth import get_current_user

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接 {user_id: {connection_id: websocket}}
        self.active_connections: Dict[int, Dict[str, WebSocket]] = {}
        # 存储连接对应的用户 {connection_id: user_id}
        self.connection_users: Dict[str, int] = {}
        # 订阅的频道 {channel: {user_id}}
        self.subscriptions: Dict[str, Set[int]] = {}
    
    async def connect(self, websocket: WebSocket, user: User, connection_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        
        if user.id not in self.active_connections:
            self.active_connections[user.id] = {}
        
        self.active_connections[user.id][connection_id] = websocket
        self.connection_users[connection_id] = user.id
        
        logger.info(f"用户 {user.username} 建立WebSocket连接: {connection_id}")
        
        # 发送连接成功消息
        await self.send_personal_message(
            WebSocketMessage(
                type="connection_established",
                data={"message": "WebSocket连接已建立", "user": user.username}
            ),
            user.id
        )
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.connection_users:
            user_id = self.connection_users[connection_id]
            
            # 移除连接
            if user_id in self.active_connections:
                self.active_connections[user_id].pop(connection_id, None)
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
            
            # 移除用户映射
            del self.connection_users[connection_id]
            
            # 移除订阅
            for channel in self.subscriptions:
                self.subscriptions[channel].discard(user_id)
            
            logger.info(f"WebSocket连接已断开: {connection_id}")
    
    async def send_personal_message(self, message: WebSocketMessage, user_id: int):
        """发送个人消息"""
        if user_id in self.active_connections:
            message_data = message.dict()
            message_data['timestamp'] = message_data['timestamp'].isoformat()
            
            # 发送到该用户的所有连接
            disconnected_connections = []
            for connection_id, websocket in self.active_connections[user_id].items():
                try:
                    await websocket.send_text(json.dumps(message_data))
                except Exception as e:
                    logger.error(f"发送消息失败 {connection_id}: {e}")
                    disconnected_connections.append(connection_id)
            
            # 清理断开的连接
            for connection_id in disconnected_connections:
                self.disconnect(connection_id)
    
    async def broadcast_to_channel(self, message: WebSocketMessage, channel: str):
        """向频道广播消息"""
        if channel in self.subscriptions:
            for user_id in self.subscriptions[channel].copy():
                await self.send_personal_message(message, user_id)
    
    async def broadcast_to_all(self, message: WebSocketMessage):
        """向所有连接广播消息"""
        for user_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, user_id)
    
    def subscribe_to_channel(self, user_id: int, channel: str):
        """订阅频道"""
        if channel not in self.subscriptions:
            self.subscriptions[channel] = set()
        self.subscriptions[channel].add(user_id)
        logger.info(f"用户 {user_id} 订阅频道: {channel}")
    
    def unsubscribe_from_channel(self, user_id: int, channel: str):
        """取消订阅频道"""
        if channel in self.subscriptions:
            self.subscriptions[channel].discard(user_id)
            if not self.subscriptions[channel]:
                del self.subscriptions[channel]
        logger.info(f"用户 {user_id} 取消订阅频道: {channel}")
    
    def get_active_users(self) -> List[int]:
        """获取活跃用户列表"""
        return list(self.active_connections.keys())
    
    def get_connection_count(self) -> int:
        """获取连接总数"""
        return sum(len(connections) for connections in self.active_connections.values())


# 全局连接管理器
manager = ConnectionManager()


class WebSocketService:
    """WebSocket服务"""
    
    @staticmethod
    async def send_task_update(task_id: str, status: str, host_id: int, host_name: str, 
                              output: str = None, error: str = None):
        """发送任务更新消息"""
        message = WebSocketMessage(
            type="task_update",
            data={
                "task_id": task_id,
                "status": status,
                "host_id": host_id,
                "host_name": host_name,
                "output": output,
                "error": error
            }
        )
        await manager.broadcast_to_channel(message, f"task:{task_id}")
    
    @staticmethod
    async def send_log_stream(host_id: int, log_type: str, content: str):
        """发送日志流消息"""
        message = WebSocketMessage(
            type="log_stream",
            data={
                "host_id": host_id,
                "log_type": log_type,
                "content": content
            }
        )
        await manager.broadcast_to_channel(message, f"host:{host_id}:logs")
    
    @staticmethod
    async def send_system_alert(alert_type: str, title: str, message: str, level: str = "info"):
        """发送系统告警消息"""
        alert_message = WebSocketMessage(
            type="system_alert",
            data={
                "alert_type": alert_type,
                "title": title,
                "message": message,
                "level": level
            }
        )
        await manager.broadcast_to_all(alert_message)
    
    @staticmethod
    async def send_host_status_update(host_id: int, status: str, last_connected: datetime = None):
        """发送主机状态更新"""
        message = WebSocketMessage(
            type="host_status_update",
            data={
                "host_id": host_id,
                "status": status,
                "last_connected": last_connected.isoformat() if last_connected else None
            }
        )
        await manager.broadcast_to_channel(message, "host_status")
    
    @staticmethod
    async def send_user_notification(user_id: int, title: str, message: str, 
                                   notification_type: str = "info"):
        """发送用户通知"""
        notification = WebSocketMessage(
            type="user_notification",
            data={
                "title": title,
                "message": message,
                "notification_type": notification_type
            }
        )
        await manager.send_personal_message(notification, user_id)


async def handle_websocket_message(websocket: WebSocket, user: User, message_data: dict):
    """处理WebSocket消息"""
    message_type = message_data.get("type")
    data = message_data.get("data", {})
    
    if message_type == "subscribe":
        # 订阅频道
        channel = data.get("channel")
        if channel:
            manager.subscribe_to_channel(user.id, channel)
            await websocket.send_text(json.dumps({
                "type": "subscription_confirmed",
                "data": {"channel": channel}
            }))
    
    elif message_type == "unsubscribe":
        # 取消订阅频道
        channel = data.get("channel")
        if channel:
            manager.unsubscribe_from_channel(user.id, channel)
            await websocket.send_text(json.dumps({
                "type": "unsubscription_confirmed",
                "data": {"channel": channel}
            }))
    
    elif message_type == "ping":
        # 心跳检测
        await websocket.send_text(json.dumps({
            "type": "pong",
            "data": {"timestamp": datetime.utcnow().isoformat()}
        }))
    
    elif message_type == "get_status":
        # 获取状态信息
        await websocket.send_text(json.dumps({
            "type": "status_info",
            "data": {
                "active_users": len(manager.get_active_users()),
                "total_connections": manager.get_connection_count(),
                "subscriptions": list(manager.subscriptions.keys())
            }
        }))
    
    else:
        # 未知消息类型
        await websocket.send_text(json.dumps({
            "type": "error",
            "data": {"message": f"未知消息类型: {message_type}"}
        }))


# 导出服务实例
websocket_service = WebSocketService()
