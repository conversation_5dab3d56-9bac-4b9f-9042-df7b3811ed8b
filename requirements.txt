# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1
databases[postgresql]==0.8.0
asyncpg==0.29.0
aiosqlite==0.19.0

# SSH 连接
paramiko==3.3.1
asyncssh==2.14.0

# 异步任务队列
celery==5.3.4
redis==5.0.1

# 工具库
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
aiofiles==23.2.1

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# 监控和日志
structlog==23.2.0
prometheus-client==0.19.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# WebSocket支持
websockets==12.0

# 定时任务
apscheduler==3.10.4

# 限流和安全
slowapi==0.1.9
python-ipware==2.0.3

# 系统监控
psutil==5.9.6
py-cpuinfo==9.0.0

# 文件处理
python-magic==0.4.27
chardet==5.2.0

# 邮件通知
emails==0.6.0

# 配置管理
pydantic-settings==2.1.0