#!/usr/bin/env python3
"""
数据库初始化脚本
创建默认用户和基础数据
"""
import asyncio
import os
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import Base
from app.models import User, HostGroup, UserRole
from app.auth import AuthManager
from app.config import settings

async def init_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    
    # 创建数据库引擎
    engine = create_async_engine(settings.database_url, echo=True)
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    print("✅ 数据库表创建完成")
    
    # 创建会话
    AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with AsyncSessionLocal() as session:
        # 检查是否已有用户
        from sqlalchemy.future import select
        result = await session.execute(select(User))
        existing_users = result.scalars().all()
        
        if not existing_users:
            print("👤 创建默认用户...")
            
            # 创建超级管理员
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="系统管理员",
                hashed_password=AuthManager.get_password_hash("admin123"),
                role=UserRole.SUPER_ADMIN,
                is_active=True,
                is_verified=True
            )
            session.add(admin_user)
            
            # 创建普通用户
            demo_user = User(
                username="demo",
                email="<EMAIL>",
                full_name="演示用户",
                hashed_password=AuthManager.get_password_hash("demo123"),
                role=UserRole.OPERATOR,
                is_active=True,
                is_verified=True
            )
            session.add(demo_user)
            
            await session.commit()
            print("✅ 默认用户创建完成")
            print("   - 管理员: admin / admin123")
            print("   - 演示用户: demo / demo123")
        else:
            print("ℹ️ 用户已存在，跳过创建")
        
        # 检查是否已有主机分组
        result = await session.execute(select(HostGroup))
        existing_groups = result.scalars().all()
        
        if not existing_groups:
            print("📁 创建默认主机分组...")
            
            # 创建默认分组
            default_groups = [
                HostGroup(
                    name="生产环境",
                    description="生产环境服务器",
                    color="#dc3545"
                ),
                HostGroup(
                    name="测试环境",
                    description="测试环境服务器",
                    color="#ffc107"
                ),
                HostGroup(
                    name="开发环境",
                    description="开发环境服务器",
                    color="#28a745"
                )
            ]
            
            for group in default_groups:
                session.add(group)
            
            await session.commit()
            print("✅ 默认主机分组创建完成")
        else:
            print("ℹ️ 主机分组已存在，跳过创建")
    
    await engine.dispose()
    print("🎉 数据库初始化完成！")


async def create_sample_data():
    """创建示例数据"""
    print("📝 创建示例数据...")
    
    engine = create_async_engine(settings.database_url)
    AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with AsyncSessionLocal() as session:
        from app.models import ScriptTemplate
        from sqlalchemy.future import select
        
        # 检查是否已有脚本模板
        result = await session.execute(select(ScriptTemplate))
        existing_templates = result.scalars().all()
        
        if not existing_templates:
            print("📜 创建示例脚本模板...")
            
            sample_templates = [
                ScriptTemplate(
                    name="系统信息查看",
                    description="查看系统基本信息",
                    content="""#!/bin/bash
echo "=== 系统信息 ==="
uname -a
echo ""
echo "=== 内存使用情况 ==="
free -h
echo ""
echo "=== 磁盘使用情况 ==="
df -h
echo ""
echo "=== 负载情况 ==="
uptime""",
                    category="系统管理",
                    version="1.0.0"
                ),
                ScriptTemplate(
                    name="服务状态检查",
                    description="检查指定服务的运行状态",
                    content="""#!/bin/bash
SERVICE_NAME="{{service_name}}"
echo "检查服务: $SERVICE_NAME"
systemctl status $SERVICE_NAME
echo ""
echo "服务是否运行:"
systemctl is-active $SERVICE_NAME""",
                    category="服务管理",
                    parameters=[
                        {
                            "name": "service_name",
                            "type": "string",
                            "required": True,
                            "description": "要检查的服务名称",
                            "default": "nginx"
                        }
                    ],
                    version="1.0.0"
                ),
                ScriptTemplate(
                    name="日志清理",
                    description="清理指定天数前的日志文件",
                    content="""#!/bin/bash
LOG_DIR="{{log_dir}}"
DAYS="{{days}}"

echo "清理 $LOG_DIR 目录中 $DAYS 天前的日志文件..."
find $LOG_DIR -name "*.log" -type f -mtime +$DAYS -exec ls -la {} \\;
echo ""
echo "确认删除以上文件吗？(y/N)"
read -r confirm
if [[ $confirm == [yY] ]]; then
    find $LOG_DIR -name "*.log" -type f -mtime +$DAYS -delete
    echo "日志清理完成"
else
    echo "取消清理"
fi""",
                    category="维护",
                    parameters=[
                        {
                            "name": "log_dir",
                            "type": "string",
                            "required": True,
                            "description": "日志目录路径",
                            "default": "/var/log"
                        },
                        {
                            "name": "days",
                            "type": "integer",
                            "required": True,
                            "description": "保留天数",
                            "default": "7"
                        }
                    ],
                    version="1.0.0"
                )
            ]
            
            for template in sample_templates:
                session.add(template)
            
            await session.commit()
            print("✅ 示例脚本模板创建完成")
        else:
            print("ℹ️ 脚本模板已存在，跳过创建")
    
    await engine.dispose()
    print("✅ 示例数据创建完成")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Linux Management Console 2.0 - 数据库初始化")
    print("=" * 60)
    
    # 确保必要的目录存在
    os.makedirs(settings.upload_dir, exist_ok=True)
    os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
    
    # 运行初始化
    asyncio.run(init_database())
    
    # 询问是否创建示例数据
    create_samples = input("\n是否创建示例数据？(y/N): ").lower().strip()
    if create_samples in ['y', 'yes']:
        asyncio.run(create_sample_data())
    
    print("\n" + "=" * 60)
    print("🎉 初始化完成！现在可以启动应用了:")
    print("   python start.py")
    print("=" * 60)


if __name__ == "__main__":
    main()
