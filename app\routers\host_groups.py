"""
主机分组管理API路由
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.models import (
    HostGroupCreate, HostGroupUpdate, HostGroupResponse, User
)
from app.crud import HostGroupCRUD, AuditLogCRUD
from app.auth import get_current_active_user, require_host_write

router = APIRouter(prefix="/api/host-groups", tags=["host-groups"])


@router.post("/", response_model=HostGroupResponse, status_code=status.HTTP_201_CREATED)
async def create_host_group(
    group_data: HostGroupCreate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """创建主机分组"""
    try:
        group = await HostGroupCRUD.create(db, group_data, current_user.id)
        
        # 记录操作
        await AuditLogCRUD.create(
            db, current_user.id, "host_group_created", "host_group", str(group.id),
            {"name": group.name}, request.client.host
        )
        
        return group
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建主机分组失败: {str(e)}"
        )


@router.get("/", response_model=List[HostGroupResponse])
async def get_host_groups(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取主机分组列表"""
    groups = await HostGroupCRUD.get_all(db, skip=skip, limit=limit)
    return groups


@router.get("/{group_id}", response_model=HostGroupResponse)
async def get_host_group(
    group_id: int,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个主机分组"""
    group = await HostGroupCRUD.get(db, group_id)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机分组不存在"
        )
    return group


@router.put("/{group_id}", response_model=HostGroupResponse)
async def update_host_group(
    group_id: int,
    group_data: HostGroupUpdate,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """更新主机分组"""
    group = await HostGroupCRUD.update(db, group_id, group_data)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机分组不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_group_updated", "host_group", str(group_id),
        group_data.dict(exclude_unset=True), request.client.host
    )
    
    return group


@router.delete("/{group_id}")
async def delete_host_group(
    group_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """删除主机分组"""
    success = await HostGroupCRUD.delete(db, group_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机分组不存在"
        )
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_group_deleted", "host_group", str(group_id),
        {}, request.client.host
    )
    
    return {"message": "主机分组删除成功"}


@router.post("/{group_id}/hosts/{host_id}")
async def add_host_to_group(
    group_id: int,
    host_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """将主机添加到分组"""
    from app.crud import HostCRUD
    
    # 检查分组是否存在
    group = await HostGroupCRUD.get(db, group_id)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机分组不存在"
        )
    
    # 检查主机是否存在
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    
    # 更新主机分组
    host.group_id = group_id
    await db.commit()
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_added_to_group", "host", str(host_id),
        {"group_id": group_id, "group_name": group.name}, request.client.host
    )
    
    return {"message": f"主机 {host.name} 已添加到分组 {group.name}"}


@router.delete("/{group_id}/hosts/{host_id}")
async def remove_host_from_group(
    group_id: int,
    host_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """从分组中移除主机"""
    from app.crud import HostCRUD
    
    # 检查主机是否存在且在该分组中
    host = await HostCRUD.get(db, host_id)
    if not host:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="主机不存在"
        )
    
    if host.group_id != group_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="主机不在该分组中"
        )
    
    # 移除主机分组
    host.group_id = None
    await db.commit()
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, "host_removed_from_group", "host", str(host_id),
        {"group_id": group_id}, request.client.host
    )
    
    return {"message": f"主机 {host.name} 已从分组中移除"}


@router.post("/batch-operation")
async def batch_group_operation(
    operation: str,  # "add_to_group", "remove_from_group", "move_to_group"
    host_ids: List[int],
    group_id: int = None,
    target_group_id: int = None,
    request: Request = None,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(require_host_write)
):
    """批量分组操作"""
    from app.crud import HostCRUD
    
    if not host_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请选择至少一个主机"
        )
    
    # 获取主机列表
    hosts = await HostCRUD.get_by_ids(db, host_ids)
    if not hosts:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到指定的主机"
        )
    
    results = []
    
    if operation == "add_to_group":
        if not group_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请指定目标分组"
            )
        
        # 检查分组是否存在
        group = await HostGroupCRUD.get(db, group_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标分组不存在"
            )
        
        # 批量添加到分组
        for host in hosts:
            host.group_id = group_id
            results.append(f"主机 {host.name} 已添加到分组 {group.name}")
    
    elif operation == "remove_from_group":
        # 批量移除分组
        for host in hosts:
            if host.group_id:
                host.group_id = None
                results.append(f"主机 {host.name} 已从分组中移除")
            else:
                results.append(f"主机 {host.name} 未在任何分组中")
    
    elif operation == "move_to_group":
        if not target_group_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请指定目标分组"
            )
        
        # 检查目标分组是否存在
        target_group = await HostGroupCRUD.get(db, target_group_id)
        if not target_group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标分组不存在"
            )
        
        # 批量移动到目标分组
        for host in hosts:
            host.group_id = target_group_id
            results.append(f"主机 {host.name} 已移动到分组 {target_group.name}")
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的操作类型"
        )
    
    await db.commit()
    
    # 记录操作
    await AuditLogCRUD.create(
        db, current_user.id, f"batch_{operation}", "host", None,
        {
            "operation": operation,
            "host_ids": host_ids,
            "group_id": group_id,
            "target_group_id": target_group_id,
            "affected_count": len(hosts)
        },
        request.client.host if request else None
    )
    
    return {
        "message": f"批量操作完成，影响 {len(hosts)} 个主机",
        "results": results
    }
